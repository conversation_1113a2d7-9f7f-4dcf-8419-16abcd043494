// Integração com Sunmi SDK para impressão térmica
// Compatível com Sunmi V1s-G (Android 6.0)

interface SunmiPrinter {
  init(): Promise<void>
  setAlignment(alignment: number): Promise<void>
  setFontSize(size: number): Promise<void>
  printText(text: string): Promise<void>
  lineWrap(lines: number): Promise<void>
  printBitmap(bitmap: string): Promise<void>
  cutPaper(): Promise<void>
}

interface SunmiAPI {
  printer: SunmiPrinter
  isAvailable(): boolean
}

declare global {
  interface Window {
    sunmi?: SunmiAPI
    SunmiPrinter?: any
  }
}

export class SunmiPrinterService {
  private printer: SunmiPrinter | null = null
  private isInitialized = false

  constructor() {
    this.initializePrinter()
  }

  private async initializePrinter() {
    try {
      // Verificar se está rodando no Sunmi
      if (typeof window !== 'undefined') {
        // Tentar carregar SDK do Sunmi
        if (window.sunmi) {
          this.printer = window.sunmi.printer
          await this.printer?.init()
          this.isInitialized = true
          console.log('Sunmi Printer inicializado com sucesso')
        } else if (window.SunmiPrinter) {
          // Fallback para versões antigas do SDK
          this.printer = new window.SunmiPrinter()
          await this.printer?.init()
          this.isInitialized = true
          console.log('Sunmi Printer (legacy) inicializado')
        } else {
          console.log('Sunmi SDK não encontrado - usando fallback web')
        }
      }
    } catch (error) {
      console.error('Erro ao inicializar Sunmi Printer:', error)
    }
  }

  public isAvailable(): boolean {
    return this.isInitialized && this.printer !== null
  }

  public async imprimirBilhete(bilheteData: {
    codigo: string
    data: string
    apostas: Array<{
      time1: string
      time2: string
      logo1: string
      logo2: string
      data: string
      horario: string
      local: string
      tipoAposta: string
      odd: number
      valor: number
    }>
    valorTotal: number
    possibleReturn: number
  }): Promise<boolean> {
    if (!this.isAvailable()) {
      console.log('Impressora não disponível - usando impressão web')
      this.imprimirWeb(bilheteData)
      return false
    }

    try {
      const { codigo, data, apostas, valorTotal, possibleReturn } = bilheteData

      // Cabeçalho
      await this.printer!.setAlignment(1) // Centro
      await this.printer!.setFontSize(24)
      await this.printer!.printText('BOLÃO BRASIL\n')
      await this.printer!.setFontSize(18)
      await this.printer!.printText('Sistema de Apostas Esportivas\n')
      await this.printer!.printText('pos.mmapay.pro\n')
      await this.printer!.printText('================================\n')

      // Informações do bilhete
      await this.printer!.setAlignment(0) // Esquerda
      await this.printer!.setFontSize(16)
      await this.printer!.printText(`Código: ${codigo}\n`)
      await this.printer!.printText(`Data: ${data}\n`)
      await this.printer!.printText('================================\n')

      // Seleções
      await this.printer!.setFontSize(18)
      await this.printer!.printText(`SELEÇÕES: ${apostas.length}/11\n`)
      await this.printer!.printText('--------------------------------\n')

      for (let i = 0; i < apostas.length; i++) {
        const aposta = apostas[i]
        
        await this.printer!.setFontSize(16)
        await this.printer!.printText(`${i + 1}. ${aposta.time1} x ${aposta.time2}\n`)
        
        await this.printer!.setFontSize(14)
        await this.printer!.printText(`   ${aposta.data} - ${aposta.horario}\n`)
        await this.printer!.printText(`   ${aposta.local}\n`)
        await this.printer!.printText(`   ${aposta.tipoAposta}\n`)
        
        await this.printer!.setFontSize(16)
        await this.printer!.printText(`   Odd: ${aposta.odd} | Valor: R$ ${aposta.valor.toFixed(2)}\n`)
        
        if (i < apostas.length - 1) {
          await this.printer!.printText('................................\n')
        }
      }

      await this.printer!.printText('================================\n')

      // Totais
      await this.printer!.setFontSize(18)
      await this.printer!.printText(`VALOR TOTAL: R$ ${valorTotal.toFixed(2)}\n`)
      await this.printer!.printText(`RETORNO POSSÍVEL: R$ ${possibleReturn.toFixed(2)}\n`)
      await this.printer!.printText('================================\n')

      // QR Code (se suportado)
      try {
        const qrData = `https://pos.mmapay.pro/bilhete/${codigo}`
        await this.printer!.setAlignment(1) // Centro
        await this.printer!.printText('Consulte seu bilhete:\n')
        // await this.printer!.printQRCode(qrData, 200, 200) // Se suportado
        await this.printer!.printText(`${qrData}\n`)
      } catch (error) {
        console.log('QR Code não suportado')
      }

      // Rodapé
      await this.printer!.setAlignment(1) // Centro
      await this.printer!.setFontSize(12)
      await this.printer!.printText('\n')
      await this.printer!.printText('Aposte com responsabilidade\n')
      await this.printer!.printText('Suporte: pos.mmapay.pro\n')
      await this.printer!.printText('\n')
      await this.printer!.printText('Obrigado pela preferência!\n')

      // Finalizar
      await this.printer!.lineWrap(3)
      
      // Cortar papel (se suportado)
      try {
        await this.printer!.cutPaper()
      } catch (error) {
        console.log('Corte de papel não suportado')
      }

      console.log('Bilhete impresso com sucesso na impressora térmica')
      return true

    } catch (error) {
      console.error('Erro ao imprimir na impressora térmica:', error)
      this.imprimirWeb(bilheteData)
      return false
    }
  }

  private imprimirWeb(bilheteData: any) {
    // Fallback para impressão web
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Bilhete ${bilheteData.codigo}</title>
          <style>
            body { 
              font-family: 'Courier New', monospace; 
              font-size: 12px; 
              margin: 0; 
              padding: 10px; 
              width: 58mm; 
              line-height: 1.2;
            }
            .center { text-align: center; }
            .bold { font-weight: bold; }
            .line { border-bottom: 1px dashed #000; margin: 5px 0; }
            .logo { width: 12px; height: 12px; }
            @media print {
              body { margin: 0; padding: 5px; }
            }
          </style>
        </head>
        <body>
          <div class="center bold">BOLÃO BRASIL</div>
          <div class="center">Sistema de Apostas Esportivas</div>
          <div class="center">pos.mmapay.pro</div>
          <div class="line"></div>
          
          <div>Código: <span class="bold">${bilheteData.codigo}</span></div>
          <div>Data: ${bilheteData.data}</div>
          <div class="line"></div>
          
          <div class="bold">SELEÇÕES: ${bilheteData.apostas.length}/11</div>
          <div class="line"></div>
          
          ${bilheteData.apostas.map((aposta: any, index: number) => `
            <div style="margin-bottom: 8px;">
              <div class="bold">${index + 1}. ${aposta.time1} x ${aposta.time2}</div>
              <div>${aposta.data} - ${aposta.horario}</div>
              <div>${aposta.local}</div>
              <div>${aposta.tipoAposta}</div>
              <div>Odd: ${aposta.odd} | Valor: R$ ${aposta.valor.toFixed(2)}</div>
              ${index < bilheteData.apostas.length - 1 ? '<div style="border-bottom: 1px dotted #ccc; margin: 5px 0;"></div>' : ''}
            </div>
          `).join('')}
          
          <div class="line"></div>
          <div class="bold">VALOR TOTAL: R$ ${bilheteData.valorTotal.toFixed(2)}</div>
          <div class="bold">RETORNO POSSÍVEL: R$ ${bilheteData.possibleReturn.toFixed(2)}</div>
          <div class="line"></div>
          
          <div class="center">Aposte com responsabilidade</div>
          <div class="center">Suporte: pos.mmapay.pro</div>
          <div class="center">Obrigado pela preferência!</div>
        </body>
        </html>
      `)
      printWindow.document.close()
      printWindow.print()
    }
  }

  public async testarImpressora(): Promise<boolean> {
    if (!this.isAvailable()) {
      return false
    }

    try {
      await this.printer!.setAlignment(1)
      await this.printer!.setFontSize(18)
      await this.printer!.printText('TESTE DE IMPRESSORA\n')
      await this.printer!.printText('Bolão Brasil\n')
      await this.printer!.printText('Sunmi V1s-G\n')
      await this.printer!.lineWrap(2)
      return true
    } catch (error) {
      console.error('Erro no teste da impressora:', error)
      return false
    }
  }
}

// Instância global
export const sunmiPrinter = new SunmiPrinterService()
