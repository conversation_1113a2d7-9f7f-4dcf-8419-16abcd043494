import type { Campeonato, Partida, Time } from "@/lib/api-futebol"

// Dados simulados de times com URLs de escudos mais confiáveis
export const TIMES: Record<string, Time> = {
  flamengo: {
    time_id: 1,
    nome: "Flamengo",
    slug: "flamengo",
    sigla: "FLA",
    escudo:
      "https://upload.wikimedia.org/wikipedia/commons/thumb/9/93/Flamengo-RJ_%28BRA%29.png/120px-Flamengo-RJ_%28BRA%29.png",
  },
  palmeiras: {
    time_id: 2,
    nome: "Palmeiras",
    slug: "palmeiras",
    sigla: "PAL",
    escudo: "https://upload.wikimedia.org/wikipedia/commons/thumb/1/10/Palmeiras_logo.svg/120px-Palmeiras_logo.svg.png",
  },
  corinthians: {
    time_id: 3,
    nome: "Corinthians",
    slug: "corinthians",
    sigla: "COR",
    escudo: "https://upload.wikimedia.org/wikipedia/en/thumb/5/5a/Corinthians_logo.svg/120px-Corinthians_logo.svg.png",
  },
  sao_paulo: {
    time_id: 4,
    nome: "São Paulo",
    slug: "sao-paulo",
    sigla: "SAO",
    escudo:
      "https://upload.wikimedia.org/wikipedia/commons/thumb/6/6f/Brasao_do_Sao_Paulo_Futebol_Clube.svg/120px-Brasao_do_Sao_Paulo_Futebol_Clube.svg.png",
  },
  santos: {
    time_id: 5,
    nome: "Santos",
    slug: "santos",
    sigla: "SAN",
    escudo: "https://upload.wikimedia.org/wikipedia/commons/thumb/3/35/Santos_logo.svg/120px-Santos_logo.svg.png",
  },
  botafogo: {
    time_id: 6,
    nome: "Botafogo",
    slug: "botafogo",
    sigla: "BOT",
    escudo:
      "https://upload.wikimedia.org/wikipedia/commons/thumb/5/52/Botafogo_de_Futebol_e_Regatas_logo.svg/120px-Botafogo_de_Futebol_e_Regatas_logo.svg.png",
  },
  fluminense: {
    time_id: 7,
    nome: "Fluminense",
    slug: "fluminense",
    sigla: "FLU",
    escudo:
      "https://upload.wikimedia.org/wikipedia/commons/thumb/a/af/Fluminense_fc_logo.svg/120px-Fluminense_fc_logo.svg.png",
  },
  vasco: {
    time_id: 8,
    nome: "Vasco",
    slug: "vasco",
    sigla: "VAS",
    escudo: "https://upload.wikimedia.org/wikipedia/pt/thumb/a/ac/CRVascodaGama.png/120px-CRVascodaGama.png",
  },
  gremio: {
    time_id: 9,
    nome: "Grêmio",
    slug: "gremio",
    sigla: "GRE",
    escudo: "https://upload.wikimedia.org/wikipedia/commons/thumb/1/1a/Gremio_logo.svg/120px-Gremio_logo.svg.png",
  },
  internacional: {
    time_id: 10,
    nome: "Internacional",
    slug: "internacional",
    sigla: "INT",
    escudo:
      "https://upload.wikimedia.org/wikipedia/commons/thumb/f/f1/Escudo_do_Sport_Club_Internacional.svg/120px-Escudo_do_Sport_Club_Internacional.svg.png",
  },
  atletico_mg: {
    time_id: 11,
    nome: "Atlético-MG",
    slug: "atletico-mg",
    sigla: "CAM",
    escudo:
      "https://upload.wikimedia.org/wikipedia/commons/thumb/5/5c/Atletico_mineiro_galo.png/120px-Atletico_mineiro_galo.png",
  },
  ceara: {
    time_id: 12,
    nome: "Ceará",
    slug: "ceara",
    sigla: "CEA",
    escudo:
      "https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/Ceara_sporting_club_logo.png/120px-Ceara_sporting_club_logo.png",
  },
  // Adicionando mais times para completar 11 jogos
  cruzeiro: {
    time_id: 13,
    nome: "Cruzeiro",
    slug: "cruzeiro",
    sigla: "CRU",
    escudo:
      "https://upload.wikimedia.org/wikipedia/commons/thumb/9/90/Cruzeiro_Esporte_Clube_%28logo%29.svg/120px-Cruzeiro_Esporte_Clube_%28logo%29.svg.png",
  },
  bahia: {
    time_id: 14,
    nome: "Bahia",
    slug: "bahia",
    sigla: "BAH",
    escudo:
      "https://upload.wikimedia.org/wikipedia/pt/thumb/5/54/EsporteclubebahiaBrasil.png/120px-EsporteclubebahiaBrasil.png",
  },
  fortaleza: {
    time_id: 15,
    nome: "Fortaleza",
    slug: "fortaleza",
    sigla: "FOR",
    escudo:
      "https://upload.wikimedia.org/wikipedia/commons/thumb/4/40/FortalezaEsporteClube.svg/120px-FortalezaEsporteClube.svg.png",
  },
  athletico: {
    time_id: 16,
    nome: "Athletico-PR",
    slug: "athletico-pr",
    sigla: "CAP",
    escudo:
      "https://upload.wikimedia.org/wikipedia/commons/thumb/8/8c/Athletico_Paranaense.svg/120px-Athletico_Paranaense.svg.png",
  },
  bragantino: {
    time_id: 17,
    nome: "RB Bragantino",
    slug: "rb-bragantino",
    sigla: "RBB",
    escudo:
      "https://upload.wikimedia.org/wikipedia/commons/thumb/9/9e/RB_Bragantino_logo.svg/120px-RB_Bragantino_logo.svg.png",
  },
  sport: {
    time_id: 18,
    nome: "Sport",
    slug: "sport",
    sigla: "SPT",
    escudo:
      "https://upload.wikimedia.org/wikipedia/commons/thumb/1/15/Sport_Club_do_Recife_%28logo%29.svg/120px-Sport_Club_do_Recife_%28logo%29.svg.png",
  },
  chapecoense: {
    time_id: 19,
    nome: "Chapecoense",
    slug: "chapecoense",
    sigla: "CHA",
    escudo: "https://upload.wikimedia.org/wikipedia/en/thumb/9/9b/Chapecoense_logo.svg/120px-Chapecoense_logo.svg.png",
  },
  cuiaba: {
    time_id: 20,
    nome: "Cuiabá",
    slug: "cuiaba",
    sigla: "CUI",
    escudo:
      "https://upload.wikimedia.org/wikipedia/commons/thumb/1/1c/Cuiaba_Esporte_Clube_logo.svg/120px-Cuiaba_Esporte_Clube_logo.svg.png",
  },
  vitoria: {
    time_id: 21,
    nome: "Vitória",
    slug: "vitoria",
    sigla: "VIT",
    escudo:
      "https://upload.wikimedia.org/wikipedia/commons/thumb/7/70/Esporte_Clube_Vit%C3%B3ria_logo.svg/120px-Esporte_Clube_Vit%C3%B3ria_logo.svg.png",
  },
  juventude: {
    time_id: 22,
    nome: "Juventude",
    slug: "juventude",
    sigla: "JUV",
    escudo:
      "https://upload.wikimedia.org/wikipedia/commons/thumb/e/e4/Esporte_Clube_Juventude_logo.svg/120px-Esporte_Clube_Juventude_logo.svg.png",
  },
}

// Campeonatos simulados
export const CAMPEONATOS: Campeonato[] = [
  {
    campeonato_id: 1,
    nome: "Campeonato Brasileiro",
    slug: "campeonato-brasileiro",
    nome_popular: "Série A",
    edicao_atual: {
      edicao_id: 101,
      temporada: "2025",
      nome: "Campeonato Brasileiro 2025",
      nome_popular: "Brasileirão 2025",
      slug: "campeonato-brasileiro-2025",
    },
    fase_atual: {
      fase_id: 201,
      nome: "Fase Única",
      slug: "fase-unica",
      tipo: "pontos-corridos",
      _link: "/v1/campeonatos/1/fases/201",
    },
    rodada_atual: {
      nome: "15ª Rodada",
      slug: "15a-rodada",
      rodada: 15,
      status: "andamento",
      _link: "/v1/campeonatos/1/rodadas/15",
    },
    status: "andamento",
    tipo: "Pontos Corridos",
    logo: "https://logodetimes.com/competicoes/brasileiro-serie-a/brasileiro-serie-a-256.png",
    regiao: "nacional",
    _link: "/v1/campeonatos/1",
  },
]

// Função para gerar data e hora para os próximos dias
const getDataHora = (diasAFrente: number, hora: string) => {
  const data = new Date()
  data.setDate(data.getDate() + diasAFrente)
  const ano = data.getFullYear()
  const mes = String(data.getMonth() + 1).padStart(2, "0")
  const dia = String(data.getDate()).padStart(2, "0")
  return {
    data: `${ano}-${mes}-${dia}`,
    hora,
  }
}

// Estádios
const ESTADIOS = {
  maracana: "Maracanã",
  morumbi: "Morumbi",
  arena_corinthians: "Neo Química Arena",
  mineirao: "Mineirão",
  beira_rio: "Beira-Rio",
  allianz_parque: "Allianz Parque",
  vila_belmiro: "Vila Belmiro",
  castelao: "Castelão",
  fonte_nova: "Arena Fonte Nova",
  nilton_santos: "Nilton Santos",
  arena_pantanal: "Arena Pantanal",
}

// Gerar exatamente 11 partidas para o sistema de apostas
export const gerarPartidas = (): { serieA: Partida[]; serieB: Partida[]; copaDoBrasil: Partida[] } => {
  // Série A - 11 jogos para completar o sistema de apostas
  const serieA: Partida[] = [
    {
      partida_id: 1001,
      campeonato: { campeonato_id: 1, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
      rodada: "15ª Rodada",
      stadium: ESTADIOS.maracana,
      ...getDataHora(0, "20:00"),
      data_realizacao_iso: new Date().toISOString(),
      time_mandante: TIMES.flamengo,
      time_visitante: TIMES.palmeiras,
      placar_mandante: null,
      placar_visitante: null,
      status: "agendado",
      slug: "flamengo-vs-palmeiras",
      _link: "/v1/partidas/1001",
    },
    {
      partida_id: 1002,
      campeonato: { campeonato_id: 1, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
      rodada: "15ª Rodada",
      stadium: ESTADIOS.arena_corinthians,
      ...getDataHora(0, "16:00"),
      data_realizacao_iso: new Date().toISOString(),
      time_mandante: TIMES.corinthians,
      time_visitante: TIMES.sao_paulo,
      placar_mandante: null,
      placar_visitante: null,
      status: "agendado",
      slug: "corinthians-vs-sao-paulo",
      _link: "/v1/partidas/1002",
    },
    {
      partida_id: 1003,
      campeonato: { campeonato_id: 1, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
      rodada: "15ª Rodada",
      stadium: ESTADIOS.nilton_santos,
      ...getDataHora(0, "19:00"),
      data_realizacao_iso: new Date().toISOString(),
      time_mandante: TIMES.botafogo,
      time_visitante: TIMES.ceara,
      placar_mandante: null,
      placar_visitante: null,
      status: "agendado",
      slug: "botafogo-vs-ceara",
      _link: "/v1/partidas/1003",
    },
    {
      partida_id: 1004,
      campeonato: { campeonato_id: 1, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
      rodada: "15ª Rodada",
      stadium: ESTADIOS.mineirao,
      ...getDataHora(0, "21:30"),
      data_realizacao_iso: new Date().toISOString(),
      time_mandante: TIMES.atletico_mg,
      time_visitante: TIMES.cruzeiro,
      placar_mandante: null,
      placar_visitante: null,
      status: "agendado",
      slug: "atletico-mg-vs-cruzeiro",
      _link: "/v1/partidas/1004",
    },
    {
      partida_id: 1005,
      campeonato: { campeonato_id: 1, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
      rodada: "15ª Rodada",
      stadium: ESTADIOS.beira_rio,
      ...getDataHora(1, "16:00"),
      data_realizacao_iso: new Date().toISOString(),
      time_mandante: TIMES.internacional,
      time_visitante: TIMES.gremio,
      placar_mandante: null,
      placar_visitante: null,
      status: "agendado",
      slug: "internacional-vs-gremio",
      _link: "/v1/partidas/1005",
    },
    {
      partida_id: 1006,
      campeonato: { campeonato_id: 1, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
      rodada: "15ª Rodada",
      stadium: ESTADIOS.vila_belmiro,
      ...getDataHora(1, "18:30"),
      data_realizacao_iso: new Date().toISOString(),
      time_mandante: TIMES.santos,
      time_visitante: TIMES.fluminense,
      placar_mandante: null,
      placar_visitante: null,
      status: "agendado",
      slug: "santos-vs-fluminense",
      _link: "/v1/partidas/1006",
    },
    {
      partida_id: 1007,
      campeonato: { campeonato_id: 1, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
      rodada: "15ª Rodada",
      stadium: ESTADIOS.fonte_nova,
      ...getDataHora(1, "20:00"),
      data_realizacao_iso: new Date().toISOString(),
      time_mandante: TIMES.bahia,
      time_visitante: TIMES.vasco,
      placar_mandante: null,
      placar_visitante: null,
      status: "agendado",
      slug: "bahia-vs-vasco",
      _link: "/v1/partidas/1007",
    },
    {
      partida_id: 1008,
      campeonato: { campeonato_id: 1, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
      rodada: "15ª Rodada",
      stadium: ESTADIOS.castelao,
      ...getDataHora(2, "16:00"),
      data_realizacao_iso: new Date().toISOString(),
      time_mandante: TIMES.fortaleza,
      time_visitante: TIMES.athletico,
      placar_mandante: null,
      placar_visitante: null,
      status: "agendado",
      slug: "fortaleza-vs-athletico",
      _link: "/v1/partidas/1008",
    },
    {
      partida_id: 1009,
      campeonato: { campeonato_id: 1, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
      rodada: "15ª Rodada",
      stadium: "Estádio Nabi Abi Chedid",
      ...getDataHora(2, "18:30"),
      data_realizacao_iso: new Date().toISOString(),
      time_mandante: TIMES.bragantino,
      time_visitante: TIMES.sport,
      placar_mandante: null,
      placar_visitante: null,
      status: "agendado",
      slug: "bragantino-vs-sport",
      _link: "/v1/partidas/1009",
    },
    {
      partida_id: 1010,
      campeonato: { campeonato_id: 1, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
      rodada: "15ª Rodada",
      stadium: "Arena Condá",
      ...getDataHora(2, "20:00"),
      data_realizacao_iso: new Date().toISOString(),
      time_mandante: TIMES.chapecoense,
      time_visitante: TIMES.cuiaba,
      placar_mandante: null,
      placar_visitante: null,
      status: "agendado",
      slug: "chapecoense-vs-cuiaba",
      _link: "/v1/partidas/1010",
    },
    {
      partida_id: 1011,
      campeonato: { campeonato_id: 1, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
      rodada: "15ª Rodada",
      stadium: "Estádio Barradão",
      ...getDataHora(3, "16:00"),
      data_realizacao_iso: new Date().toISOString(),
      time_mandante: TIMES.vitoria,
      time_visitante: TIMES.juventude,
      placar_mandante: null,
      placar_visitante: null,
      status: "agendado",
      slug: "vitoria-vs-juventude",
      _link: "/v1/partidas/1011",
    },
  ]

  return { serieA, serieB: [], copaDoBrasil: [] }
}
