import type { Campeonato, Partida, Tabela, Time } from "@/types/api-futebol"

// API de futebol real
export class ApiFutebol {
  private static readonly API_URL = "https://api.api-futebol.com.br/v1"
  private static readonly API_TOKEN = "live_81a2cc1684e041f15d909022b3caf7" // Token real

  // Método para fazer requisições à API
  private static async fetchData<T>(endpoint: string): Promise<T> {
    try {
      // Em ambiente de produção, usamos a API real
      if (process.env.NODE_ENV === "production") {
        const response = await fetch(`${this.API_URL}${endpoint}`, {
          headers: {
            Authorization: `Bearer ${this.API_TOKEN}`,
            "Content-Type": "application/json",
          },
        })

        if (!response.ok) {
          throw new Error(`Erro na API: ${response.status} ${response.statusText}`)
        }

        return await response.json()
      } else {
        // Em desenvolvimento, podemos usar dados mockados para economizar requisições
        return this.getMockData<T>(endpoint)
      }
    } catch (error) {
      console.error(`Erro ao buscar dados da API: ${endpoint}`, error)
      // Em caso de erro, tentamos usar os dados mockados como fallback
      return this.getMockData<T>(endpoint)
    }
  }

  // Método para obter dados mockados
  private static async getMockData<T>(endpoint: string): Promise<T> {
    // Simular delay de rede
    await new Promise((resolve) => setTimeout(resolve, 300))

    if (endpoint.includes("/campeonatos")) {
      if (endpoint.endsWith("/tabela")) {
        return this.getMockTabela() as unknown as T
      } else if (endpoint.includes("/partidas")) {
        return this.getMockPartidas() as unknown as T
      } else if (endpoint === "/partidas/hoje") {
        return this.getMockPartidas() as unknown as T
      } else {
        return this.getMockCampeonatos() as unknown as T
      }
    } else if (endpoint === "/partidas/hoje") {
      return this.getMockPartidas() as unknown as T
    }

    throw new Error(`Endpoint não suportado: ${endpoint}`)
  }

  // Buscar todos os campeonatos
  static async getCampeonatos(): Promise<Campeonato[]> {
    return this.fetchData<Campeonato[]>("/campeonatos")
  }

  // Buscar tabela de um campeonato
  static async getTabelaCampeonato(campeonatoId: number): Promise<Tabela> {
    return this.fetchData<Tabela>(`/campeonatos/${campeonatoId}/tabela`)
  }

  // Buscar partidas de um campeonato
  static async getPartidasCampeonato(campeonatoId: number): Promise<Partida[]> {
    return this.fetchData<Partida[]>(`/campeonatos/${campeonatoId}/partidas`)
  }

  // Buscar partidas de hoje
  static async getPartidasHoje(): Promise<Partida[]> {
    return this.fetchData<Partida[]>("/partidas/hoje")
  }

  // Buscar partidas de uma rodada específica
  static async getPartidasRodada(campeonatoId: number, rodada: number): Promise<Partida[]> {
    const rodadaData = await this.fetchData<any>(`/campeonatos/${campeonatoId}/rodadas/${rodada}`)
    return rodadaData.partidas
  }

  // Buscar informações de um time
  static async getTime(timeId: number): Promise<Time> {
    return this.fetchData<Time>(`/times/${timeId}`)
  }

  // Dados mockados para campeonatos
  private static getMockCampeonatos(): Campeonato[] {
    return [
      {
        campeonato_id: 10,
        nome: "Campeonato Brasileiro",
        slug: "campeonato-brasileiro",
        nome_popular: "Brasileirão Série A",
        edicao_atual: {
          edicao_id: 179,
          temporada: "2025",
          nome: "Campeonato Brasileiro 2025",
          nome_popular: "Brasileirão 2025",
          slug: "campeonato-brasileiro-2025",
        },
        fase_atual: {
          fase_id: 768,
          nome: "Fase única",
          slug: "fase-unica-campeonato-brasileiro-2025",
          tipo: "pontos-corridos",
          _link: "/v1/campeonatos/10/fases/768",
        },
        rodada_atual: {
          nome: "1ª Rodada",
          slug: "1a-rodada",
          rodada: 1,
          status: "agendada",
        },
        status: "andamento",
        tipo: "Pontos Corridos",
        logo: "https://api.api-futebol.com.br/images/competicao/brasileiro-seriea.png",
        regiao: "nacional",
        _link: "/v1/campeonatos/10",
      },
      {
        campeonato_id: 2,
        nome: "Copa do Brasil",
        slug: "copa-do-brasil",
        nome_popular: "Copa do Brasil",
        edicao_atual: {
          edicao_id: 173,
          temporada: "2025",
          nome: "Copa do Brasil 2025",
          nome_popular: "Copa do Brasil 2025",
          slug: "copa-do-brasil-2025",
        },
        fase_atual: {
          fase_id: 761,
          nome: "Oitavas de Final",
          slug: "oitavas-de-final",
          tipo: "mata-mata",
          _link: "/v1/campeonatos/2/fases/761",
        },
        rodada_atual: null,
        status: "andamento",
        tipo: "Mata-Mata",
        logo: "https://api.api-futebol.com.br/images/competicao/copa-do-brasil.png",
        regiao: "nacional",
        _link: "/v1/campeonatos/2",
      },
    ]
  }

  // Dados mockados para tabela
  private static getMockTabela(): Tabela {
    return {
      "fase-unica": {
        "grupo-principal": [
          {
            posicao: 1,
            pontos: 23,
            time: {
              time_id: 18,
              nome_popular: "Flamengo",
              sigla: "FLA",
              escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc73fcec1e.svg",
            },
            jogos: 11,
            vitorias: 7,
            empates: 2,
            derrotas: 2,
            gols_pro: 23,
            gols_contra: 10,
            saldo_gols: 13,
            aproveitamento: 69.7,
            variacao_posicao: 1,
            ultimos_jogos: ["v", "v", "d", "e", "v"],
          },
          {
            posicao: 2,
            pontos: 22,
            time: {
              time_id: 26,
              nome_popular: "Fluminense",
              sigla: "FLU",
              escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc750c6c81.svg",
            },
            jogos: 11,
            vitorias: 7,
            empates: 1,
            derrotas: 3,
            gols_pro: 20,
            gols_contra: 11,
            saldo_gols: 9,
            aproveitamento: 66.7,
            variacao_posicao: 1,
            ultimos_jogos: ["e", "v", "v", "v", "v"],
          },
          {
            posicao: 3,
            pontos: 21,
            time: {
              time_id: 15,
              nome_popular: "Botafogo",
              sigla: "BOT",
              escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc743454d0.svg",
            },
            jogos: 11,
            vitorias: 6,
            empates: 3,
            derrotas: 2,
            gols_pro: 20,
            gols_contra: 8,
            saldo_gols: 12,
            aproveitamento: 63.6,
            variacao_posicao: 1,
            ultimos_jogos: ["v", "e", "v", "e", "v"],
          },
          {
            posicao: 4,
            pontos: 21,
            time: {
              time_id: 21,
              nome_popular: "Vasco",
              sigla: "VAS",
              escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc702ef04f.svg",
            },
            jogos: 11,
            vitorias: 6,
            empates: 3,
            derrotas: 2,
            gols_pro: 18,
            gols_contra: 13,
            saldo_gols: 5,
            aproveitamento: 63.6,
            variacao_posicao: -3,
            ultimos_jogos: ["v", "v", "e", "e", "d"],
          },
          {
            posicao: 5,
            pontos: 17,
            time: {
              time_id: 23,
              nome_popular: "Ceará",
              sigla: "CEA",
              escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc7c25cc4f.svg",
            },
            jogos: 11,
            vitorias: 4,
            empates: 5,
            derrotas: 2,
            gols_pro: 21,
            gols_contra: 15,
            saldo_gols: 6,
            aproveitamento: 51.5,
            variacao_posicao: 1,
            ultimos_jogos: ["e", "v", "v", "e", "v"],
          },
        ],
      },
    }
  }

  // Dados mockados para partidas
  private static getMockPartidas(): Partida[] {
    const hoje = new Date()
    const dataHoje = `${hoje.getDate().toString().padStart(2, "0")}/${(hoje.getMonth() + 1)
      .toString()
      .padStart(2, "0")}/${hoje.getFullYear()}`

    return [
      {
        partida_id: 1001,
        campeonato: { campeonato_id: 10, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
        time_mandante: {
          time_id: 22,
          nome_popular: "Botafogo",
          sigla: "BOT",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc743454d0.svg",
        },
        time_visitante: {
          time_id: 23,
          nome_popular: "Ceará",
          sigla: "CEA",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc7c25cc4f.svg",
        },
        placar_mandante: null,
        placar_visitante: null,
        status: "agendado",
        slug: "botafogo-ceara",
        data_realizacao: dataHoje,
        hora_realizacao: "18:00",
        data_realizacao_iso: hoje.toISOString(),
        estadio: {
          estadio_id: 1,
          nome_popular: "Nilton Santos",
        },
        _link: "/v1/partidas/1001",
      },
      {
        partida_id: 1002,
        campeonato: { campeonato_id: 10, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
        time_mandante: {
          time_id: 18,
          nome_popular: "Flamengo",
          sigla: "FLA",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc73fcec1e.svg",
        },
        time_visitante: {
          time_id: 26,
          nome_popular: "Fluminense",
          sigla: "FLU",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc750c6c81.svg",
        },
        placar_mandante: null,
        placar_visitante: null,
        status: "agendado",
        slug: "flamengo-fluminense",
        data_realizacao: dataHoje,
        hora_realizacao: "20:00",
        data_realizacao_iso: hoje.toISOString(),
        estadio: {
          estadio_id: 2,
          nome_popular: "Maracanã",
        },
        _link: "/v1/partidas/1002",
      },
      {
        partida_id: 1003,
        campeonato: { campeonato_id: 10, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
        time_mandante: {
          time_id: 19,
          nome_popular: "Chapecoense",
          sigla: "CHA",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc7667b316.svg",
        },
        time_visitante: {
          time_id: 20,
          nome_popular: "Cuiabá",
          sigla: "CUI",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fb9ff2c293.png",
        },
        placar_mandante: null,
        placar_visitante: null,
        status: "agendado",
        slug: "chapecoense-cuiaba",
        data_realizacao: dataHoje,
        hora_realizacao: "16:00",
        data_realizacao_iso: hoje.toISOString(),
        estadio: {
          estadio_id: 3,
          nome_popular: "Arena Condá",
        },
        _link: "/v1/partidas/1003",
      },
      {
        partida_id: 1004,
        campeonato: { campeonato_id: 10, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
        time_mandante: {
          time_id: 21,
          nome_popular: "Vitória",
          sigla: "VIT",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc7a4add00.svg",
        },
        time_visitante: {
          time_id: 22,
          nome_popular: "Juventude",
          sigla: "JUV",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fca933f3f2.svg",
        },
        placar_mandante: null,
        placar_visitante: null,
        status: "agendado",
        slug: "vitoria-juventude",
        data_realizacao: dataHoje,
        hora_realizacao: "19:00",
        data_realizacao_iso: hoje.toISOString(),
        estadio: {
          estadio_id: 4,
          nome_popular: "Estádio Barradão",
        },
        _link: "/v1/partidas/1004",
      },
      {
        partida_id: 1005,
        campeonato: { campeonato_id: 10, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
        time_mandante: {
          time_id: 24,
          nome_popular: "Sport",
          sigla: "SPT",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc7c25cc4f.svg",
        },
        time_visitante: {
          time_id: 25,
          nome_popular: "Novorizontino",
          sigla: "NOV",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fba0fa4353.png",
        },
        placar_mandante: null,
        placar_visitante: null,
        status: "agendado",
        slug: "sport-novorizontino",
        data_realizacao: dataHoje,
        hora_realizacao: "21:30",
        data_realizacao_iso: hoje.toISOString(),
        estadio: {
          estadio_id: 5,
          nome_popular: "Ilha do Retiro",
        },
        _link: "/v1/partidas/1005",
      },
      {
        partida_id: 1006,
        campeonato: { campeonato_id: 10, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
        time_mandante: {
          time_id: 27,
          nome_popular: "Cruzeiro",
          sigla: "CRU",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc7c25cc4f.svg",
        },
        time_visitante: {
          time_id: 28,
          nome_popular: "Atlético-MG",
          sigla: "CAM",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc7c25cc4f.svg",
        },
        placar_mandante: null,
        placar_visitante: null,
        status: "agendado",
        slug: "cruzeiro-atletico-mg",
        data_realizacao: dataHoje,
        hora_realizacao: "16:00",
        data_realizacao_iso: hoje.toISOString(),
        estadio: {
          estadio_id: 6,
          nome_popular: "Mineirão",
        },
        _link: "/v1/partidas/1006",
      },
      {
        partida_id: 1007,
        campeonato: { campeonato_id: 10, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
        time_mandante: {
          time_id: 29,
          nome_popular: "Palmeiras",
          sigla: "PAL",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc7c25cc4f.svg",
        },
        time_visitante: {
          time_id: 30,
          nome_popular: "Corinthians",
          sigla: "COR",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc7c25cc4f.svg",
        },
        placar_mandante: null,
        placar_visitante: null,
        status: "agendado",
        slug: "palmeiras-corinthians",
        data_realizacao: dataHoje,
        hora_realizacao: "18:30",
        data_realizacao_iso: hoje.toISOString(),
        estadio: {
          estadio_id: 7,
          nome_popular: "Allianz Parque",
        },
        _link: "/v1/partidas/1007",
      },
      {
        partida_id: 1008,
        campeonato: { campeonato_id: 10, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
        time_mandante: {
          time_id: 31,
          nome_popular: "São Paulo",
          sigla: "SAO",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc7c25cc4f.svg",
        },
        time_visitante: {
          time_id: 32,
          nome_popular: "Santos",
          sigla: "SAN",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc7c25cc4f.svg",
        },
        placar_mandante: null,
        placar_visitante: null,
        status: "agendado",
        slug: "sao-paulo-santos",
        data_realizacao: dataHoje,
        hora_realizacao: "20:30",
        data_realizacao_iso: hoje.toISOString(),
        estadio: {
          estadio_id: 8,
          nome_popular: "Morumbi",
        },
        _link: "/v1/partidas/1008",
      },
      {
        partida_id: 1009,
        campeonato: { campeonato_id: 10, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
        time_mandante: {
          time_id: 33,
          nome_popular: "Grêmio",
          sigla: "GRE",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc7c25cc4f.svg",
        },
        time_visitante: {
          time_id: 34,
          nome_popular: "Internacional",
          sigla: "INT",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc7c25cc4f.svg",
        },
        placar_mandante: null,
        placar_visitante: null,
        status: "agendado",
        slug: "gremio-internacional",
        data_realizacao: dataHoje,
        hora_realizacao: "21:00",
        data_realizacao_iso: hoje.toISOString(),
        estadio: {
          estadio_id: 9,
          nome_popular: "Arena do Grêmio",
        },
        _link: "/v1/partidas/1009",
      },
      {
        partida_id: 1010,
        campeonato: { campeonato_id: 10, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
        time_mandante: {
          time_id: 35,
          nome_popular: "Bahia",
          sigla: "BAH",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc7c25cc4f.svg",
        },
        time_visitante: {
          time_id: 36,
          nome_popular: "Fortaleza",
          sigla: "FOR",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc7c25cc4f.svg",
        },
        placar_mandante: null,
        placar_visitante: null,
        status: "agendado",
        slug: "bahia-fortaleza",
        data_realizacao: dataHoje,
        hora_realizacao: "19:30",
        data_realizacao_iso: hoje.toISOString(),
        estadio: {
          estadio_id: 10,
          nome_popular: "Arena Fonte Nova",
        },
        _link: "/v1/partidas/1010",
      },
      {
        partida_id: 1011,
        campeonato: { campeonato_id: 10, nome: "Campeonato Brasileiro", slug: "campeonato-brasileiro" },
        time_mandante: {
          time_id: 37,
          nome_popular: "Athletico-PR",
          sigla: "CAP",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc7c25cc4f.svg",
        },
        time_visitante: {
          time_id: 38,
          nome_popular: "Bragantino",
          sigla: "RBB",
          escudo: "https://cdn.api-futebol.com.br/times/escudos/677fc7c25cc4f.svg",
        },
        placar_mandante: null,
        placar_visitante: null,
        status: "agendado",
        slug: "athletico-pr-bragantino",
        data_realizacao: dataHoje,
        hora_realizacao: "17:00",
        data_realizacao_iso: hoje.toISOString(),
        estadio: {
          estadio_id: 11,
          nome_popular: "Arena da Baixada",
        },
        _link: "/v1/partidas/1011",
      },
    ]
  }
}
