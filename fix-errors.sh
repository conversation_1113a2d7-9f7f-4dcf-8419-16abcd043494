#!/bin/bash

# Script para corrigir todos os erros automaticamente
echo "🔧 Corrigindo erros do sistema..."

# Cores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[AVISO]${NC} $1"
}

error() {
    echo -e "${RED}[ERRO]${NC} $1"
}

# 1. Limpar cache do npm
log "Limpando cache do npm..."
npm cache clean --force 2>/dev/null || true

# 2. Corrigir configurações NPM
log "Corrigindo configurações NPM..."
cat > .npmrc << 'EOF'
fund=false
audit-level=moderate
save-exact=true
prefer-offline=true
EOF

# 3. Reinstalar dependências
log "Reinstalando dependências..."
rm -rf node_modules package-lock.json
npm install --no-fund --no-audit

# 3. Gerar Prisma Client
log "Gerando Prisma Client..."
npx prisma generate

# 4. Sincronizar banco de dados
log "Sincronizando banco de dados..."
npx prisma db push

# 5. Executar seed se necessário
if [ ! -f "prisma/prod.db" ]; then
    log "Executando seed do banco..."
    npx tsx prisma/seed.ts
fi

# 6. Criar diretórios necessários
log "Criando diretórios necessários..."
mkdir -p public/icons
mkdir -p public/logos
mkdir -p public/downloads

# 7. Verificar se ícones existem
if [ ! -f "public/icons/icon-192x192.png" ]; then
    log "Criando ícones faltantes..."
    # Criar ícone simples SVG
    cat > public/icons/icon-192x192.png << 'EOF'
<svg width="192" height="192" viewBox="0 0 192 192" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="192" height="192" rx="24" fill="#1e40af"/>
<path d="M96 48C76.118 48 60 64.118 60 84C60 103.882 76.118 120 96 120C115.882 120 132 103.882 132 84C132 64.118 115.882 48 96 48Z" fill="white"/>
<path d="M96 144C76.118 144 60 160.118 60 180H84C84 166.745 94.745 156 108 156C121.255 156 132 166.745 132 180H156C156 160.118 139.882 144 120 144H96Z" fill="white"/>
</svg>
EOF
fi

if [ ! -f "public/icons/icon-16x16.png" ]; then
    cat > public/icons/icon-16x16.png << 'EOF'
<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="16" height="16" rx="2" fill="#1e40af"/>
<path d="M8 4C6.34315 4 5 5.34315 5 7C5 8.65685 6.34315 10 8 10C9.65685 10 11 8.65685 11 7C11 5.34315 9.65685 4 8 4Z" fill="white"/>
<path d="M8 12C6.34315 12 5 13.3431 5 15H7C7 13.8954 7.89543 13 9 13C10.1046 13 11 13.8954 11 15H13C13 13.3431 11.6569 12 10 12H8Z" fill="white"/>
</svg>
EOF
fi

# 8. Verificar se manifest.json existe
if [ ! -f "public/manifest.json" ]; then
    log "Criando manifest.json..."
    cat > public/manifest.json << 'EOF'
{
  "name": "Bolão Brasil",
  "short_name": "Bolão",
  "description": "Sistema de apostas esportivas",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#1e40af",
  "theme_color": "#1e40af",
  "icons": [
    {
      "src": "/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-16x16.png",
      "sizes": "16x16",
      "type": "image/png"
    }
  ]
}
EOF
fi

# 9. Build do projeto
log "Fazendo build do projeto..."
npm run build

# 10. Verificar se há erros
if [ $? -eq 0 ]; then
    log "✅ Todos os erros foram corrigidos!"
    echo ""
    echo "🎉 Sistema pronto para uso!"
    echo ""
    echo "📋 Para iniciar o servidor:"
    echo "   npm run dev"
    echo ""
    echo "📱 Para gerar APK Sunmi:"
    echo "   ./build-complete.sh"
    echo ""
    echo "🌐 Acesse: https://pos.mmapay.pro"
else
    error "❌ Ainda há erros no projeto!"
    echo ""
    echo "🔧 Tente executar manualmente:"
    echo "   npm install"
    echo "   npm run build"
    echo "   npm run dev"
fi

log "Script de correção finalizado!"
