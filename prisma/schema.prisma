// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String
  balance   Float    @default(0.0)
  isA<PERSON><PERSON>  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relacionamentos
  apostas      Aposta[]
  transacoes   Transacao[]

  @@map("users")
}

model Partida {
  id                String    @id @default(cuid())
  partidaId         Int       @unique // ID da API externa
  campeonatoId      Int
  campeonatoNome    String
  rodada            String
  timeMandanteId    Int
  timeMandanteNome  String
  timeMandanteSigla String
  timeMandanteEscudo String
  timeVisitanteId   Int
  timeVisitanteNome String
  timeVisitanteSigla String
  timeVisitanteEscudo String
  dataRealizacao    DateTime
  horaRealizacao    String
  status            String    @default("agendado")
  placarMandante    Int?
  placarVisitante   Int?
  estadio           String?
  slug              String
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relacionamentos
  apostas Aposta[]

  @@map("partidas")
}

model Aposta {
  id        String   @id @default(cuid())
  userId    String
  partidaId String
  tipo      String   // "1", "X", "2", "over", "under", etc.
  valor     Float
  odd       Float
  status    String   @default("pendente") // "pendente", "ganha", "perdida", "cancelada"
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relacionamentos
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  partida Partida @relation(fields: [partidaId], references: [id], onDelete: Cascade)

  @@map("apostas")
}

model Transacao {
  id        String   @id @default(cuid())
  userId    String
  tipo      String   // "deposito", "saque", "aposta", "premio"
  valor     Float
  descricao String
  status    String   @default("concluida") // "pendente", "concluida", "cancelada"
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relacionamentos
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("transacoes")
}

model Campeonato {
  id           String @id @default(cuid())
  campeonatoId Int    @unique
  nome         String
  slug         String
  nomePopular  String
  temporada    String
  status       String @default("ativo")
  logo         String?
  regiao       String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("campeonatos")
}

model Time {
  id          String @id @default(cuid())
  timeId      Int    @unique
  nome        String
  nomePopular String
  sigla       String
  escudo      String
  apelido     String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("times")
}
