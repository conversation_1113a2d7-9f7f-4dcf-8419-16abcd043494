import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Iniciando seed do banco de dados...')

  // Criar usuário admin
  const adminPassword = await bcrypt.hash('admin123', 10)
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      password: adminPassword,
      balance: 10000.0,
      isAdmin: true,
    },
  })

  // Criar usuário teste
  const userPassword = await bcrypt.hash('123456', 10)
  const user = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      password: userPassword,
      balance: 1000.0,
      isAdmin: false,
    },
  })

  // Criar campeonatos
  const brasileirao = await prisma.campeonato.upsert({
    where: { campeonatoId: 10 },
    update: {},
    create: {
      campeonatoId: 10,
      nome: 'Campeonato Brasileiro',
      slug: 'campeonato-brasileiro',
      nomePopular: 'Brasileirão Série A',
      temporada: '2025',
      status: 'ativo',
      regiao: 'Brasil',
    },
  })

  // Criar times
  const times = [
    { timeId: 1, nome: 'Clube de Regatas do Flamengo', nomePopular: 'Flamengo', sigla: 'FLA', escudo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/93/Flamengo-RJ_%28BRA%29.png/120px-Flamengo-RJ_%28BRA%29.png' },
    { timeId: 2, nome: 'Sociedade Esportiva Palmeiras', nomePopular: 'Palmeiras', sigla: 'PAL', escudo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/10/Palmeiras_logo.svg/120px-Palmeiras_logo.svg.png' },
    { timeId: 3, nome: 'Sport Club Corinthians Paulista', nomePopular: 'Corinthians', sigla: 'COR', escudo: 'https://upload.wikimedia.org/wikipedia/en/thumb/5/5a/Corinthians_logo.svg/120px-Corinthians_logo.svg.png' },
    { timeId: 4, nome: 'São Paulo Futebol Clube', nomePopular: 'São Paulo', sigla: 'SAO', escudo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/6f/Brasao_do_Sao_Paulo_Futebol_Clube.svg/120px-Brasao_do_Sao_Paulo_Futebol_Clube.svg.png' },
    { timeId: 5, nome: 'Clube Atlético Mineiro', nomePopular: 'Atlético-MG', sigla: 'CAM', escudo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Atletico_mineiro_galo.png/120px-Atletico_mineiro_galo.png' },
    { timeId: 6, nome: 'Grêmio Foot-Ball Porto Alegrense', nomePopular: 'Grêmio', sigla: 'GRE', escudo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/1a/Gremio.svg/120px-Gremio.svg.png' },
    { timeId: 7, nome: 'Sport Club Internacional', nomePopular: 'Internacional', sigla: 'INT', escudo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f1/Escudo_do_Sport_Club_Internacional.svg/120px-Escudo_do_Sport_Club_Internacional.svg.png' },
    { timeId: 8, nome: 'Clube de Regatas Vasco da Gama', nomePopular: 'Vasco', sigla: 'VAS', escudo: 'https://upload.wikimedia.org/wikipedia/pt/thumb/a/ac/CRVascodaGama.png/120px-CRVascodaGama.png' },
  ]

  for (const time of times) {
    await prisma.time.upsert({
      where: { timeId: time.timeId },
      update: {},
      create: time,
    })
  }

  // Criar partidas de exemplo
  const hoje = new Date()
  const amanha = new Date(hoje)
  amanha.setDate(hoje.getDate() + 1)

  const partidas = [
    {
      partidaId: 1001,
      campeonatoId: 10,
      campeonatoNome: 'Campeonato Brasileiro',
      rodada: '15ª Rodada',
      timeMandanteId: 1,
      timeMandanteNome: 'Flamengo',
      timeMandanteSigla: 'FLA',
      timeMandanteEscudo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/93/Flamengo-RJ_%28BRA%29.png/120px-Flamengo-RJ_%28BRA%29.png',
      timeVisitanteId: 2,
      timeVisitanteNome: 'Palmeiras',
      timeVisitanteSigla: 'PAL',
      timeVisitanteEscudo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/10/Palmeiras_logo.svg/120px-Palmeiras_logo.svg.png',
      dataRealizacao: amanha,
      horaRealizacao: '20:00',
      status: 'agendado',
      estadio: 'Maracanã',
      slug: 'flamengo-vs-palmeiras',
    },
    {
      partidaId: 1002,
      campeonatoId: 10,
      campeonatoNome: 'Campeonato Brasileiro',
      rodada: '15ª Rodada',
      timeMandanteId: 3,
      timeMandanteNome: 'Corinthians',
      timeMandanteSigla: 'COR',
      timeMandanteEscudo: 'https://upload.wikimedia.org/wikipedia/en/thumb/5/5a/Corinthians_logo.svg/120px-Corinthians_logo.svg.png',
      timeVisitanteId: 4,
      timeVisitanteNome: 'São Paulo',
      timeVisitanteSigla: 'SAO',
      timeVisitanteEscudo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/6f/Brasao_do_Sao_Paulo_Futebol_Clube.svg/120px-Brasao_do_Sao_Paulo_Futebol_Clube.svg.png',
      dataRealizacao: amanha,
      horaRealizacao: '18:30',
      status: 'agendado',
      estadio: 'Neo Química Arena',
      slug: 'corinthians-vs-sao-paulo',
    },
  ]

  for (const partida of partidas) {
    await prisma.partida.upsert({
      where: { partidaId: partida.partidaId },
      update: {},
      create: partida,
    })
  }

  // Criar transações de exemplo
  await prisma.transacao.create({
    data: {
      userId: admin.id,
      tipo: 'deposito',
      valor: 10000.0,
      descricao: 'Depósito inicial do administrador',
      status: 'concluida',
    },
  })

  await prisma.transacao.create({
    data: {
      userId: user.id,
      tipo: 'deposito',
      valor: 1000.0,
      descricao: 'Depósito inicial do usuário teste',
      status: 'concluida',
    },
  })

  console.log('✅ Seed concluído com sucesso!')
  console.log('👤 Admin criado:', admin.email)
  console.log('👤 Usuário teste criado:', user.email)
  console.log('🏆 Campeonatos criados:', 1)
  console.log('⚽ Times criados:', times.length)
  console.log('🎯 Partidas criadas:', partidas.length)
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error('❌ Erro no seed:', e)
    await prisma.$disconnect()
    process.exit(1)
  })
