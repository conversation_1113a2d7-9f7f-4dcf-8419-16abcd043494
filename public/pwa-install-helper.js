// This script helps with PWA installation on Android devices
// It's loaded in the main layout to provide additional installation support

;(() => {
  // Check if running on Android
  const isAndroid = /android/i.test(navigator.userAgent)

  if (!isAndroid) return

  // Check if already installed
  if (window.matchMedia("(display-mode: standalone)").matches) return

  // Track if we've already shown installation instructions
  let installInstructionsShown = false

  // Function to show manual installation instructions for Android browsers
  // that don't properly support beforeinstallprompt
  function showManualInstallInstructions() {
    if (installInstructionsShown) return
    installInstructionsShown = true

    // Check if we should show instructions (not shown in last 3 days)
    const lastShown = localStorage.getItem("pwa-manual-instructions-shown")
    if (lastShown && Date.now() - Number.parseInt(lastShown) < 3 * 24 * 60 * 60 * 1000) {
      return
    }

    // Store that we've shown instructions
    localStorage.setItem("pwa-manual-instructions-shown", Date.now().toString())

    // We'll rely on our main PWA install button component
    // This is just a fallback to ensure the event is triggered
    console.log("📱 Android device detected, installation helper active")
  }

  // For Samsung Internet and other Android browsers that may not fire beforeinstallprompt
  setTimeout(showManualInstallInstructions, 5000)

  // Listen for app installation
  window.addEventListener("appinstalled", (event) => {
    console.log("🎉 PWA was installed")
    localStorage.setItem("pwa-installed", "true")
  })
})()
