const CACHE_NAME = "bolao-brasil-v1.0.1"
const urlsToCache = [
  "/",
  "/bilhetes",
  "/bet-slip",
  "/perfil",
  "/transacoes",
  "/admin",
  "/widgets",
  "/manifest.json",
  "/icons/icon-192x192.png",
  "/icons/icon-512x512.png",
]

// Install event
self.addEventListener("install", (event) => {
  console.log("🔧 Service Worker installing...")
  event.waitUntil(
    caches
      .open(CACHE_NAME)
      .then((cache) => {
        console.log("📦 Cache opened")
        return cache.addAll(urlsToCache)
      })
      .then(() => {
        console.log("✅ Service Worker installation complete")
        return self.skipWaiting()
      })
      .catch((error) => {
        console.error("❌ Cache installation failed:", error)
      }),
  )
})

// Fetch event
self.addEventListener("fetch", (event) => {
  event.respondWith(
    caches
      .match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        if (response) {
          return response
        }
        return fetch(event.request).then((response) => {
          // Don't cache if not a valid response
          if (!response || response.status !== 200 || response.type !== "basic") {
            return response
          }

          // Clone the response
          const responseToCache = response.clone()

          // Add to cache for future use
          caches.open(CACHE_NAME).then((cache) => {
            cache.put(event.request, responseToCache)
          })

          return response
        })
      })
      .catch(() => {
        // Return offline page if available
        if (event.request.destination === "document") {
          return caches.match("/")
        }
      }),
  )
})

// Activate event
self.addEventListener("activate", (event) => {
  console.log("🚀 Service Worker activating...")
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log("🗑️ Deleting old cache:", cacheName)
            return caches.delete(cacheName)
          }
        }),
      ).then(() => {
        console.log("✅ Service Worker now active")
        return self.clients.claim()
      })
    }),
  )
})

// Background sync for offline functionality
self.addEventListener("sync", (event) => {
  console.log("🔄 Background sync event:", event.tag)
  if (event.tag === "background-sync") {
    event.waitUntil(doBackgroundSync())
  }
})

function doBackgroundSync() {
  console.log("🔄 Performing background sync")
  // Sync offline data when connection is restored
  return Promise.resolve()
}

// Push notifications
self.addEventListener("push", (event) => {
  console.log("📬 Push notification received:", event)
  const options = {
    body: event.data ? event.data.text() : "Nova notificação do Bolão Brasil",
    icon: "/icons/icon-192x192.png",
    badge: "/icons/icon-72x72.png",
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1,
    },
    actions: [
      {
        action: "explore",
        title: "Ver Detalhes",
        icon: "/icons/icon-96x96.png",
      },
      {
        action: "close",
        title: "Fechar",
        icon: "/icons/icon-96x96.png",
      },
    ],
  }

  event.waitUntil(self.registration.showNotification("Bolão Brasil", options))
})

// Handle notification clicks
self.addEventListener("notificationclick", (event) => {
  console.log("👆 Notification clicked:", event.notification.tag)
  event.notification.close()

  if (event.action === "explore") {
    // Open the app and navigate to a specific page
    event.waitUntil(clients.openWindow("/bilhetes"))
  }
})
