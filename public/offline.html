<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bolão Brasil - Offline</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        .container {
            max-width: 400px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }
        
        .icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
        
        h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        p {
            font-size: 16px;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        
        .button {
            background: white;
            color: #1e40af;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: transform 0.2s;
        }
        
        .button:hover {
            transform: translateY(-2px);
        }
        
        .download-section {
            margin-top: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        
        .download-section h3 {
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .download-button {
            background: #10b981;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        /* Otimizações para Sunmi V1s-G */
        @media screen and (max-width: 480px) {
            body {
                padding: 10px;
            }
            
            .container {
                max-width: 100%;
                padding: 15px;
            }
            
            h1 {
                font-size: 20px;
            }
            
            p {
                font-size: 14px;
            }
            
            .button {
                padding: 10px 20px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📱</div>
        <h1>Você está offline</h1>
        <p>Não foi possível conectar à internet. Verifique sua conexão e tente novamente.</p>
        
        <button class="button" onclick="window.location.reload()">
            🔄 Tentar Novamente
        </button>
        
        <a href="/" class="button">
            🏠 Página Inicial
        </a>
        
        <div class="download-section">
            <h3>📲 Instalar App Offline</h3>
            <p style="font-size: 14px;">Para usar o Bolão Brasil offline no seu Sunmi V1s-G:</p>
            
            <a href="/downloads/bolao-brasil-sunmi.apk" class="download-button" download>
                📱 Baixar APK para Sunmi
            </a>
            
            <div style="margin-top: 15px; font-size: 12px; opacity: 0.8;">
                <p><strong>Compatível com:</strong></p>
                <p>✅ Sunmi V1s-G<br>
                ✅ Android 6.0 Marshmallow<br>
                ✅ ARM 32-bit</p>
            </div>
        </div>
    </div>
    
    <script>
        // Verificar conexão periodicamente
        function checkConnection() {
            if (navigator.onLine) {
                window.location.reload()
            }
        }
        
        // Verificar a cada 5 segundos
        setInterval(checkConnection, 5000)
        
        // Escutar eventos de conexão
        window.addEventListener('online', () => {
            window.location.reload()
        })
        
        // Registrar Service Worker se disponível
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(() => console.log('SW registrado'))
                .catch(() => console.log('Erro ao registrar SW'))
        }
    </script>
</body>
</html>
