export interface Campeonato {
  campeonato_id: number
  nome: string
  slug: string
  nome_popular: string
  edicao_atual?: {
    edicao_id: number
    temporada: string
    nome: string
    nome_popular: string
    slug: string
  }
  fase_atual?: {
    fase_id: number
    nome: string
    slug: string
    tipo: string
    _link: string
  }
  rodada_atual?: any
  status: string
  tipo: string
  logo: string
  regiao: string
  _link: string
}

export interface Time {
  time_id: number
  nome_popular: string
  sigla: string
  escudo: string
  nome?: string
  apelido?: string
}

export interface Partida {
  partida_id: number
  campeonato: {
    campeonato_id: number
    nome: string
    slug: string
  }
  placar?: string
  time_mandante: Time
  time_visitante: Time
  placar_mandante: number | null
  placar_visitante: number | null
  disputa_penalti?: boolean
  status: string
  slug: string
  data_realizacao: string
  hora_realizacao: string
  data_realizacao_iso: string
  estadio?: {
    estadio_id: number
    nome_popular: string
  }
  _link: string
}

export interface TabelaItem {
  posicao: number
  pontos: number
  time: Time
  jogos: number
  vitorias: number
  empates: number
  derrotas: number
  gols_pro: number
  gols_contra: number
  saldo_gols: number
  aproveitamento: number
  variacao_posicao: number
  ultimos_jogos: string[]
}

export interface Tabela {
  [fase: string]: {
    [grupo: string]: TabelaItem[]
  }
}
