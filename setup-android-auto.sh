#!/bin/bash

# Script de configuração automática do Android SDK para Sunmi V1s-G
echo "🚀 Configuração automática do Android SDK para Sunmi V1s-G..."

# Detectar sistema operacional
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="mac"
else
    OS="windows"
fi

echo "📱 Sistema detectado: $OS"

# Criar diretórios
mkdir -p /tmp/android-setup
cd /tmp/android-setup

# Baixar Android SDK Command Line Tools
echo "📥 Baixando Android SDK..."
if [ "$OS" = "linux" ]; then
    wget -q https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip -O cmdtools.zip
elif [ "$OS" = "mac" ]; then
    curl -s https://dl.google.com/android/repository/commandlinetools-mac-9477386_latest.zip -o cmdtools.zip
fi

# Extrair
echo "📦 Extraindo Android SDK..."
unzip -q cmdtools.zip

# Configurar diretórios
ANDROID_HOME="/opt/android-sdk"
sudo mkdir -p $ANDROID_HOME/cmdline-tools
sudo mv cmdline-tools $ANDROID_HOME/cmdline-tools/latest
sudo chown -R $USER:$USER $ANDROID_HOME

# Configurar variáveis de ambiente
echo "⚙️ Configurando variáveis de ambiente..."
cat >> ~/.bashrc << EOF

# Android SDK para Sunmi V1s-G
export ANDROID_HOME=$ANDROID_HOME
export PATH=\$PATH:\$ANDROID_HOME/cmdline-tools/latest/bin
export PATH=\$PATH:\$ANDROID_HOME/platform-tools
export PATH=\$PATH:\$ANDROID_HOME/build-tools/28.0.3
EOF

# Aplicar variáveis
export ANDROID_HOME=$ANDROID_HOME
export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin
export PATH=$PATH:$ANDROID_HOME/platform-tools

# Instalar componentes necessários para Android 6.0
echo "📱 Instalando componentes Android 6.0..."
yes | $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager --licenses
$ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager "platform-tools"
$ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager "platforms;android-23"
$ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager "platforms;android-28"
$ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager "build-tools;28.0.3"
$ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager "system-images;android-23;google_apis;armeabi-v7a"

# Instalar Java 8 se necessário
echo "☕ Verificando Java..."
if ! java -version 2>&1 | grep -q "1.8\|11"; then
    echo "📥 Instalando OpenJDK 8..."
    if [ "$OS" = "linux" ]; then
        sudo apt update
        sudo apt install -y openjdk-8-jdk
        export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
    elif [ "$OS" = "mac" ]; then
        brew install openjdk@8
        export JAVA_HOME=/usr/local/opt/openjdk@8
    fi
fi

# Limpar
cd /
rm -rf /tmp/android-setup

echo "✅ Android SDK configurado com sucesso!"
echo "📱 Configuração para Sunmi V1s-G (Android 6.0) completa!"
echo ""
echo "🔄 Execute: source ~/.bashrc"
echo "🚀 Depois execute: ./build-sunmi-auto.sh"
