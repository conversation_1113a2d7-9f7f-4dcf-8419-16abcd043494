/** @type {import('next').NextConfig} */
const nextConfig = {
  // Configuração correta para Next.js 15.3.3
  serverExternalPackages: ['@prisma/client'],

  experimental: {
    allowedDevOrigins: ['pos.mmapay.pro', 'localhost', '127.0.0.1']
  },

  // Permitir origens para desenvolvimento
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*'
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS'
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization'
          }
        ]
      }
    ]
  },

  images: {
    unoptimized: true,
    domains: ['pos.mmapay.pro', 'localhost', '127.0.0.1']
  },

  // Otimizações para Sunmi V1s-G (Android 6.0, baixa resolução)
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // Configurações para dispositivos com pouca RAM
  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      // Otimizações para produção em dispositivos com pouca RAM
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          default: {
            minChunks: 1,
            priority: -20,
            reuseExistingChunk: true
          },
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: -10,
            chunks: 'all',
            maxSize: 200000 // Limitar tamanho dos chunks para dispositivos com pouca RAM
          }
        }
      }
    }
    return config
  }
}

module.exports = nextConfig
