# 🏆 Bolão Brasil - Sistema de Apostas Esportivas

Sistema completo de apostas esportivas com PWA (Progressive Web App) e banco de dados SQLite.

## 🚀 Funcionalidades

- ✅ Sistema de apostas em partidas de futebol
- ✅ Autenticação de usuários
- ✅ Painel administrativo
- ✅ Gestão de saldo e transações
- ✅ PWA - Instalável como app mobile
- ✅ Banco de dados SQLite com Prisma ORM
- ✅ Interface responsiva com Tailwind CSS
- ✅ Service Worker para funcionamento offline

## 🛠️ Tecnologias

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS, Radix UI
- **Database**: SQLite com Prisma ORM
- **PWA**: Service Worker, Web App Manifest
- **Auth**: Sistema próprio com bcrypt

## 📦 Instalação e Configuração

### 1. Instalar Dependências
```bash
npm install
```

### 2. Configurar Banco de Dados
```bash
# Criar e sincronizar o banco
npx prisma db push

# Popular com dados iniciais
npx tsx prisma/seed.ts
```

### 3. Iniciar Aplicação
```bash
npm run dev
```

A aplicação estará disponível em: http://localhost:3000

## 👤 Usuários de Teste

### Administrador
- **Email**: <EMAIL>
- **Senha**: admin123
- **Saldo**: R$ 10.000,00

### Usuário Comum
- **Email**: <EMAIL>
- **Senha**: 123456
- **Saldo**: R$ 1.000,00

## 📱 PWA - Instalação como App

### No Desktop (Chrome/Edge):
1. Acesse a aplicação
2. Clique no ícone de instalação na barra de endereços
3. Confirme a instalação

### No Mobile:
1. Acesse a aplicação no navegador
2. Toque no menu do navegador
3. Selecione "Adicionar à tela inicial"
4. Confirme a instalação

## 🗄️ Banco de Dados

### Estrutura das Tabelas:
- **users**: Usuários do sistema
- **partidas**: Partidas de futebol
- **apostas**: Apostas realizadas
- **transacoes**: Histórico financeiro
- **campeonatos**: Campeonatos disponíveis
- **times**: Times de futebol

### Comandos Úteis:
```bash
# Visualizar banco no Prisma Studio
npx prisma studio

# Reset do banco (cuidado!)
npx prisma db push --force-reset

# Gerar cliente Prisma
npx prisma generate
```

## 🎯 Funcionalidades Principais

### Para Usuários:
- Fazer apostas em partidas
- Visualizar histórico de apostas
- Gerenciar saldo
- Ver transações

### Para Administradores:
- Gerenciar partidas
- Visualizar todas as apostas
- Controlar usuários
- Relatórios financeiros

## 🔧 Scripts Disponíveis

```bash
npm run dev          # Iniciar em desenvolvimento
npm run build        # Build para produção
npm run start        # Iniciar em produção
npm run lint         # Verificar código
npm run db:generate  # Gerar cliente Prisma
npm run db:push      # Sincronizar banco
npm run db:seed      # Popular banco
npm run db:studio    # Abrir Prisma Studio
```

## 📂 Estrutura do Projeto

```
├── app/                 # Páginas Next.js
├── components/          # Componentes React
├── hooks/              # Custom hooks
├── lib/                # Utilitários e configurações
├── prisma/             # Schema e migrations
├── public/             # Arquivos estáticos
├── scripts/            # Scripts utilitários
└── types/              # Definições TypeScript
```

## 🌐 Deploy

### Vercel (Recomendado):
1. Conecte o repositório no Vercel
2. Configure as variáveis de ambiente
3. Deploy automático

### Outras Plataformas:
- Netlify
- Railway
- Heroku

## 🔒 Segurança

- Senhas criptografadas com bcrypt
- Validação de dados com Zod
- Sanitização de inputs
- CORS configurado

## 📄 Licença

Este projeto é para fins educacionais e demonstrativos.

## ⚡ Comando de Instalação Rápida (Testado e Funcionando)

```bash
npm cache clean --force && rm -rf node_modules && rm -f package-lock.json && npm install && npm install next@latest react@latest react-dom@latest && npm install @radix-ui/react-dialog @radix-ui/react-select @radix-ui/react-tabs @radix-ui/react-toast @radix-ui/react-tooltip @radix-ui/react-dropdown-menu && npm install tailwindcss postcss autoprefixer clsx tailwind-merge && npm install lucide-react @heroicons/react react-hook-form @hookform/resolvers zod date-fns swr axios uuid && npm install --save-dev @types/node @types/react @types/react-dom @types/uuid typescript && npm run build
```

## 🔧 Configuração de Produção (aaPanel + Nginx)

### PM2 Configuration
```bash
# ecosystem.config.js
module.exports = {
  apps: [{
    name: 'bolao-brasil',
    script: 'npm',
    args: 'start',
    cwd: '/www/wwwroot/pos.mmapay.pro',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}
```

### Nginx Proxy Configuration
```nginx
server {
    listen 80;
    listen 443 ssl;
    server_name pos.mmapay.pro;

    # SSL Configuration
    ssl_certificate /www/server/panel/vhost/cert/pos.mmapay.pro/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/pos.mmapay.pro/privkey.pem;
    ssl_protocols TLSv1.1 TLSv1.2;
    ssl_prefer_server_ciphers on;

    # HTTP to HTTPS redirect
    if ($server_port !~ 443){
        rewrite ^(/.*)$ https://$host$1 permanent;
    }

    # Proxy para aplicação Node.js
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
        proxy_connect_timeout 60;
        proxy_send_timeout 60;
    }

    # SSL Certificate verification
    location ~ \.well-known{
        allow all;
    }

    access_log  /www/wwwlogs/pos.mmapay.pro.log;
    error_log  /www/wwwlogs/pos.mmapay.pro.error.log;
}
```

## 🐛 Troubleshooting

### Problemas Comuns

#### 1. Erro de porta ocupada (EADDRINUSE)
```bash
# Verificar processos na porta 3000
lsof -i :3000

# Matar processo específico
kill -9 $(lsof -t -i:3000)

# Limpar PM2
pm2 kill

# Matar todos os processos Node.js
killall node
```

#### 2. Problemas de dependências
```bash
# Comando completo de limpeza e reinstalação
npm cache clean --force && rm -rf node_modules package-lock.json && npm install
```

#### 3. Problemas de SSL/Nginx
```bash
# Testar configuração Nginx
nginx -t

# Reiniciar Nginx
systemctl reload nginx
```

## 🚀 Deploy Status

✅ **Funcionando em produção**: https://pos.mmapay.pro
✅ **SSL**: Configurado e funcionando
✅ **PM2**: Gerenciamento de processos
✅ **Nginx**: Proxy reverso configurado
✅ **Prisma**: Banco de dados SQLite

---

**Desenvolvido com ❤️ para a comunidade brasileira de apostas esportivas**
