import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.bolaobrasil.app',
  appName: 'Bolão Brasil',
  webDir: 'out',
  server: {
    androidScheme: 'https'
  },
  android: {
    minSdkVersion: 23, // Android 6.0 Marshmallow
    compileSdkVersion: 28, // Compatível com Android 6.0
    targetSdkVersion: 23,
    buildOptions: {
      keystorePath: undefined,
      keystoreAlias: undefined,
      keystorePassword: undefined,
      keystoreKeyPassword: undefined,
      releaseType: 'APK'
    }
  },
  plugins: {
    SplashScreen: {
      launchShowDuration: 2000,
      backgroundColor: "#1e40af",
      androidSplashResourceName: 'splash',
      androidScaleType: 'CENTER_CROP',
      showSpinner: false
    },
    StatusBar: {
      style: 'DARK',
      backgroundColor: '#1e40af'
    }
  }
};

export default config;