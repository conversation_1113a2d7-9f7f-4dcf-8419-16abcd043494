const fs = require('fs');
const path = require('path');

// Criar diretório de ícones se não existir
const iconsDir = path.join(__dirname, '..', 'public', 'icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Template SVG para o ícone
const createSVGIcon = (size) => `
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4caf50;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="${size}" height="${size}" rx="${size * 0.15}" fill="url(#grad)"/>
  <circle cx="${size * 0.5}" cy="${size * 0.35}" r="${size * 0.15}" fill="white"/>
  <rect x="${size * 0.25}" y="${size * 0.55}" width="${size * 0.5}" height="${size * 0.08}" rx="${size * 0.04}" fill="white"/>
  <rect x="${size * 0.3}" y="${size * 0.68}" width="${size * 0.4}" height="${size * 0.06}" rx="${size * 0.03}" fill="white"/>
  <text x="${size * 0.5}" y="${size * 0.85}" text-anchor="middle" font-family="Arial, sans-serif" font-size="${size * 0.08}" font-weight="bold" fill="white">BB</text>
</svg>
`;

// Tamanhos necessários para PWA
const sizes = [72, 96, 128, 144, 152, 192, 384, 512];

console.log('🎨 Gerando ícones para PWA...');

sizes.forEach(size => {
  const svgContent = createSVGIcon(size);
  const filename = `icon-${size}x${size}.svg`;
  const filepath = path.join(iconsDir, filename);
  
  fs.writeFileSync(filepath, svgContent.trim());
  console.log(`✅ Criado: ${filename}`);
});

// Criar um ícone PNG simples usando canvas (se disponível)
console.log('📱 Ícones SVG criados com sucesso!');
console.log('💡 Para produção, converta os SVGs para PNG usando uma ferramenta online ou ImageMagick');
console.log('🔗 Sugestão: https://cloudconvert.com/svg-to-png');
