const { execSync } = require('child_process');
const fs = require('fs');

console.log('🏆 BOLÃO BRASIL - SETUP COMPLETO');
console.log('================================\n');

function runCommand(command, description) {
  try {
    console.log(`🔄 ${description}...`);
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} - Concluído!\n`);
  } catch (error) {
    console.error(`❌ Erro em: ${description}`);
    console.error(error.message);
    process.exit(1);
  }
}

function checkFile(filePath, description) {
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${description} - Encontrado`);
  } else {
    console.log(`❌ ${description} - Não encontrado`);
  }
}

console.log('📋 VERIFICANDO ARQUIVOS...');
checkFile('package.json', 'Package.json');
checkFile('prisma/schema.prisma', 'Schema do Prisma');
checkFile('.env', 'Arquivo de ambiente');
checkFile('public/manifest.json', 'Manifest PWA');
checkFile('public/sw.js', 'Service Worker');
console.log('');

console.log('📦 INSTALANDO DEPENDÊNCIAS...');
runCommand('npm install', 'Instalação de dependências');

console.log('🗄️ CONFIGURANDO BANCO DE DADOS...');
runCommand('npx prisma db push', 'Criação do banco SQLite');
runCommand('npx tsx prisma/seed.ts', 'População do banco com dados iniciais');

console.log('🎨 GERANDO ÍCONES PWA...');
runCommand('node scripts/generate-icons.js', 'Geração de ícones');

console.log('🚀 INICIANDO APLICAÇÃO...');
console.log('A aplicação será iniciada em http://localhost:3000');
console.log('');
console.log('👤 USUÁRIOS DE TESTE:');
console.log('Admin: <EMAIL> / admin123');
console.log('Usuário: <EMAIL> / 123456');
console.log('');
console.log('📱 PARA INSTALAR COMO PWA:');
console.log('1. Acesse http://localhost:3000');
console.log('2. Clique no ícone de instalação no navegador');
console.log('3. Confirme a instalação');
console.log('');
console.log('🔧 PARA GERAR APK:');
console.log('1. node scripts/setup-capacitor.js');
console.log('2. npm run build:mobile');
console.log('3. npx cap add android');
console.log('4. npx cap open android');
console.log('');
console.log('🎉 SETUP COMPLETO! Iniciando servidor...');

// Iniciar o servidor
execSync('npm run dev', { stdio: 'inherit' });
