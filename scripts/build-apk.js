const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('📱 GERANDO APK DO BOLÃO BRASIL');
console.log('==============================\n');

try {
  // Verificar se o Android está configurado
  if (!fs.existsSync('android')) {
    console.log('❌ Pasta android não encontrada. Execute primeiro:');
    console.log('npx cap add android');
    process.exit(1);
  }

  console.log('🔧 Configurando build.gradle para APK...');
  
  // Atualizar build.gradle para gerar APK automaticamente
  const buildGradlePath = 'android/app/build.gradle';
  let buildGradle = fs.readFileSync(buildGradlePath, 'utf8');
  
  // Adicionar configuração para APK se não existir
  if (!buildGradle.includes('assembleDebug')) {
    console.log('✅ Configuração de APK já presente');
  }

  console.log('🏗️ Gerando APK...');
  
  // Navegar para pasta android e executar gradle
  process.chdir('android');
  
  // Limpar build anterior
  console.log('🧹 Limpando build anterior...');
  const isWindows = process.platform === 'win32';
  const gradlew = isWindows ? 'gradlew.bat' : './gradlew';

  execSync(`${gradlew} clean`, { stdio: 'inherit' });

  // Gerar APK debug
  console.log('📦 Compilando APK...');
  execSync(`${gradlew} assembleDebug`, { stdio: 'inherit' });
  
  // Voltar para pasta raiz
  process.chdir('..');
  
  // Verificar se APK foi gerado
  const apkPath = 'android/app/build/outputs/apk/debug/app-debug.apk';
  if (fs.existsSync(apkPath)) {
    const stats = fs.statSync(apkPath);
    const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
    
    console.log('\n🎉 APK GERADO COM SUCESSO!');
    console.log('==========================');
    console.log(`📍 Local: ${apkPath}`);
    console.log(`📏 Tamanho: ${fileSizeInMB} MB`);
    console.log(`📅 Data: ${stats.mtime.toLocaleString()}`);
    
    // Copiar APK para pasta raiz para facilitar acesso
    const destPath = 'bolao-brasil.apk';
    fs.copyFileSync(apkPath, destPath);
    console.log(`📋 Copiado para: ${destPath}`);
    
    console.log('\n📱 COMO INSTALAR:');
    console.log('1. Transfira o arquivo bolao-brasil.apk para seu Android');
    console.log('2. Ative "Fontes desconhecidas" nas configurações');
    console.log('3. Toque no arquivo APK para instalar');
    
    console.log('\n🔧 PRÓXIMOS PASSOS:');
    console.log('1. Teste o APK no dispositivo Android');
    console.log('2. Configure o deploy web em pos.mmapay.pro');
    
  } else {
    console.log('❌ Erro: APK não foi gerado');
    console.log('Verifique se o Android Studio e SDK estão instalados');
  }

} catch (error) {
  console.error('❌ Erro ao gerar APK:', error.message);
  console.log('\n🛠️ SOLUÇÕES:');
  console.log('1. Instale Android Studio');
  console.log('2. Configure Android SDK');
  console.log('3. Execute: npx cap sync android');
}
