const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('📦 PREPARANDO ARQUIVOS PARA DEPLOY');
console.log('==================================\n');

// Criar pasta de deploy
const deployDir = 'deploy-files';
if (fs.existsSync(deployDir)) {
  fs.rmSync(deployDir, { recursive: true });
}
fs.mkdirSync(deployDir);

console.log('📁 Copiando arquivos essenciais...');

// Lista de arquivos e pastas para copiar
const filesToCopy = [
  'package.json',
  'package-lock.json',
  'next.config.production.mjs',
  'tailwind.config.ts',
  'tsconfig.json',
  'components.json',
  'postcss.config.mjs',
  '.env',
  'README.md',
  'DEPLOY_AAPANEL.md',
  'GUIA_INSTALACAO.md'
];

const foldersTooCopy = [
  'app',
  'components',
  'hooks',
  'lib',
  'prisma',
  'public',
  'styles',
  'types',
  'out' // Build de produção
];

// Copiar arquivos
filesToCopy.forEach(file => {
  if (fs.existsSync(file)) {
    const destFile = file === 'next.config.production.mjs' ? 'next.config.mjs' : file;
    fs.copyFileSync(file, path.join(deployDir, destFile));
    console.log(`✅ ${destFile}`);
  } else {
    console.log(`⚠️ ${file} - não encontrado`);
  }
});

// Copiar pastas
foldersTooCopy.forEach(folder => {
  if (fs.existsSync(folder)) {
    copyFolderSync(folder, path.join(deployDir, folder));
    console.log(`✅ ${folder}/`);
  } else {
    console.log(`⚠️ ${folder}/ - não encontrado`);
  }
});

// Criar arquivo de configuração do PM2
const pm2Config = `module.exports = {
  apps: [{
    name: 'bolao-brasil',
    script: 'npm',
    args: 'start',
    cwd: '/www/wwwroot/pos.mmapay.pro',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}`;

fs.writeFileSync(path.join(deployDir, 'ecosystem.config.js'), pm2Config);
console.log('✅ ecosystem.config.js');

// Criar script de deploy
const deployScript = `#!/bin/bash
echo "🚀 Iniciando deploy do Bolão Brasil..."

# Instalar dependências
echo "📦 Instalando dependências..."
npm install

# Configurar banco de dados
echo "🗄️ Configurando banco de dados..."
npx prisma generate
npx prisma db push
npx tsx prisma/seed.ts

# Build de produção
echo "🏗️ Gerando build de produção..."
npm run build

# Iniciar com PM2
echo "🔄 Iniciando aplicação com PM2..."
pm2 start ecosystem.config.js
pm2 save

echo "✅ Deploy concluído!"
echo "🌐 Acesse: https://pos.mmapay.pro"
`;

fs.writeFileSync(path.join(deployDir, 'deploy.sh'), deployScript);
fs.chmodSync(path.join(deployDir, 'deploy.sh'), '755');
console.log('✅ deploy.sh');

// Criar arquivo de instruções
const instructions = `# 🚀 INSTRUÇÕES DE DEPLOY

## 1. Upload dos Arquivos
- Faça upload de todos os arquivos desta pasta para: /www/wwwroot/pos.mmapay.pro

## 2. Configurar Domínio no aaPanel
- Crie o site pos.mmapay.pro
- Configure SSL (Let's Encrypt)
- Configure proxy reverso para porta 3000

## 3. Executar Deploy
\`\`\`bash
cd /www/wwwroot/pos.mmapay.pro
chmod +x deploy.sh
./deploy.sh
\`\`\`

## 4. Usuários de Teste
- Admin: <EMAIL> / admin123
- Usuário: <EMAIL> / 123456

## 5. Verificar
- Acesse: https://pos.mmapay.pro
- Teste PWA: Instalar como app
- Verifique logs: pm2 logs bolao-brasil

Consulte DEPLOY_AAPANEL.md para instruções detalhadas.
`;

fs.writeFileSync(path.join(deployDir, 'INSTRUCOES.md'), instructions);
console.log('✅ INSTRUCOES.md');

console.log('\n🎉 ARQUIVOS PREPARADOS!');
console.log('=======================');
console.log(`📁 Pasta: ${deployDir}/`);
console.log('📋 Próximos passos:');
console.log('1. Faça upload da pasta deploy-files/ para seu servidor');
console.log('2. Siga as instruções em INSTRUCOES.md');
console.log('3. Execute o script deploy.sh no servidor');

// Função auxiliar para copiar pastas
function copyFolderSync(from, to) {
  if (!fs.existsSync(to)) {
    fs.mkdirSync(to, { recursive: true });
  }
  
  fs.readdirSync(from).forEach(element => {
    const fromPath = path.join(from, element);
    const toPath = path.join(to, element);
    
    if (fs.lstatSync(fromPath).isFile()) {
      fs.copyFileSync(fromPath, toPath);
    } else {
      copyFolderSync(fromPath, toPath);
    }
  });
}
