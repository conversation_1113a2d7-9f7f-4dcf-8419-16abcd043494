const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('📱 Configurando Capacitor para gerar APK...');

try {
  // Instalar Capacitor
  console.log('📦 Instalando Capacitor...');
  execSync('npm install @capacitor/core @capacitor/cli @capacitor/android', { stdio: 'inherit' });

  // Inicializar Capacitor
  console.log('🔧 Inicializando Capacitor...');
  execSync('npx cap init "Bolão Brasil" "com.bolaobrasil.app"', { stdio: 'inherit' });

  // Criar configuração do Capacitor
  const capacitorConfig = `
import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.bolaobrasil.app',
  appName: 'Bolão Brasil',
  webDir: 'out',
  server: {
    androidScheme: 'https'
  },
  plugins: {
    SplashScreen: {
      launchShowDuration: 2000,
      backgroundColor: "#0f172a",
      showSpinner: false
    }
  }
};

export default config;
`;

  fs.writeFileSync('capacitor.config.ts', capacitorConfig.trim());
  console.log('✅ Configuração do Capacitor criada');

  // Atualizar next.config.mjs para export estático
  const nextConfig = `
/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  output: 'export',
  trailingSlash: true,
  distDir: 'out',
}

export default nextConfig
`;

  fs.writeFileSync('next.config.mjs', nextConfig.trim());
  console.log('✅ Next.js configurado para export estático');

  // Adicionar scripts ao package.json
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  packageJson.scripts = {
    ...packageJson.scripts,
    'build:mobile': 'next build && npx cap sync',
    'android:dev': 'npx cap run android',
    'android:build': 'npx cap build android',
    'ios:dev': 'npx cap run ios',
    'ios:build': 'npx cap build ios'
  };

  fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
  console.log('✅ Scripts adicionados ao package.json');

  console.log('\n🎉 Capacitor configurado com sucesso!');
  console.log('\n📋 Próximos passos:');
  console.log('1. npm run build:mobile    # Build da aplicação');
  console.log('2. npx cap add android     # Adicionar plataforma Android');
  console.log('3. npx cap open android    # Abrir no Android Studio');
  console.log('\n💡 Certifique-se de ter o Android Studio instalado!');

} catch (error) {
  console.error('❌ Erro ao configurar Capacitor:', error.message);
}
