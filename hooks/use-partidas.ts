"use client"

import { useState, useEffect } from "react"
import { ApiFutebol } from "@/lib/api-futebol"
import type { Partida } from "@/types/api-futebol"

interface UsePartidasReturn {
  partidas: Partida[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function usePartidas(): UsePartidasReturn {
  const [partidas, setPartidas] = useState<Partida[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchPartidas = async () => {
    try {
      setLoading(true)
      setError(null)

      // Busca as partidas da API
      const partidasHoje = await ApiFutebol.getPartidasHoje()
      setPartidas(partidasHoje)
    } catch (err) {
      console.error("Erro ao buscar partidas:", err)
      setError("Erro ao carregar partidas.")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPartidas()

    // Atualiza a cada 5 minutos
    const interval = setInterval(fetchPartidas, 5 * 60 * 1000)

    return () => clearInterval(interval)
  }, [])

  return {
    partidas,
    loading,
    error,
    refetch: fetchPartidas,
  }
}
