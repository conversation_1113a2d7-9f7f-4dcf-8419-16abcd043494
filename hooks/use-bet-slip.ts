"use client"

import { create } from "zustand"

interface Bet {
  id: string
  matchId: number
  betType: string
  team: string
  odd: number
  match: {
    id: number
    homeTeam: string
    homeTeamLogo: string
    awayTeam: string
    awayTeamLogo: string
    league: string
    date: string
  }
}

interface BetSlipStore {
  bets: Bet[]
  addBet: (bet: Bet) => void
  removeBet: (betId: string) => void
  clearBets: () => void
  isBetSelected: (betId: string) => boolean
  totalValue: number
  setTotalValue: (value: number) => void
  requiredSelections: number
  hasRequiredSelections: () => boolean
}

export const useBetSlip = create<BetSlipStore>((set, get) => ({
  bets: [],
  totalValue: 0,
  requiredSelections: 11, // Numero exato de selecoes necessarias

  addBet: (bet) => {
    const { bets } = get()
    // Remove any existing bet from the same match
    const filteredBets = bets.filter((b) => b.matchId !== bet.matchId)
    set({ bets: [...filteredBets, bet] })
  },

  removeBet: (betId) => {
    const { bets } = get()
    set({ bets: bets.filter((bet) => bet.id !== betId) })
  },

  clearBets: () => set({ bets: [] }),

  isBetSelected: (betId) => {
    const { bets } = get()
    return bets.some((bet) => bet.id === betId)
  },

  setTotalValue: (value) => set({ totalValue: value }),

  hasRequiredSelections: () => {
    const { bets, requiredSelections } = get()
    return bets.length === requiredSelections
  },
}))
