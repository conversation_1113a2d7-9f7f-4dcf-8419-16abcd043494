"use client"

import { create } from "zustand"

interface User {
  id: string
  name: string
  email: string
  balance: number
  isAdmin: boolean
}

interface AuthStore {
  user: User | null
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<boolean>
  logout: () => void
}

export const useAuth = create<AuthStore>((set) => ({
  user: {
    id: "1",
    name: "Admin",
    email: "<EMAIL>",
    balance: 990.0,
    isAdmin: true,
  },
  isAuthenticated: true,

  login: async (email, password) => {
    // Simulate login
    if (email === "<EMAIL>" && password === "admin123") {
      set({
        user: {
          id: "1",
          name: "Admin",
          email: "<EMAIL>",
          balance: 990.0,
          isAdmin: true,
        },
        isAuthenticated: true,
      })
      return true
    }
    return false
  },

  logout: () => set({ user: null, isAuthenticated: false }),
}))
