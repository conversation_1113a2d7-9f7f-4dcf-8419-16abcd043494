# 📱 Bolão Brasil - Configuração para Sunmi V1s-G

## 🎯 Especificações do Dispositivo

- **Modelo**: Sunmi V1s-G
- **Android**: 6.0 Marshmallow (API 23)
- **Processador**: MediaTek MT6580
- **RAM**: 1GB
- **Armazenamento**: 8GB-16GB
- **Resolução**: 480x800
- **Arquitetura**: ARM 32-bit (armeabi-v7a)

## 🛠️ Configurações Aplicadas

### ✅ Android Build
- **minSdkVersion**: 23 (Android 6.0)
- **targetSdkVersion**: 23 (Android 6.0)
- **compileSdkVersion**: 28 (Compatível)
- **Arquitetura**: armeabi-v7a (32-bit ARM)

### ✅ Otimizações
- Redução de dependências para Android 6.0
- Configuração de recursos limitados (pt, en)
- Otimização de chunks para pouca RAM
- CSS específico para resolução 480x800

## 🚀 Como Fazer o Build

### 1. Preparar Ambiente
```bash
# Instalar dependências
npm install

# Instalar Android SDK (se necessário)
# Configurar ANDROID_HOME
```

### 2. Build para Sunmi
```bash
# Dar permissão ao script
chmod +x build-sunmi.sh

# Executar build
./build-sunmi.sh
```

### 3. Instalar no Dispositivo
```bash
# Via ADB
adb install android/app/build/outputs/apk/debug/app-debug.apk

# Ou copiar APK para dispositivo e instalar manualmente
```

## 📋 Comandos Úteis

### Build Manual
```bash
# 1. Build Next.js
npm run build

# 2. Sync Capacitor
npx cap sync android

# 3. Build APK
cd android
./gradlew assembleDebug
cd ..
```

### Debug no Dispositivo
```bash
# Conectar via ADB
adb devices

# Ver logs
adb logcat | grep "BolaoBrasil"

# Instalar APK
adb install -r android/app/build/outputs/apk/debug/app-debug.apk
```

### Verificar Compatibilidade
```bash
# Verificar informações do APK
aapt dump badging android/app/build/outputs/apk/debug/app-debug.apk

# Verificar arquitetura
unzip -l android/app/build/outputs/apk/debug/app-debug.apk | grep "lib/"
```

## 🎨 Interface Otimizada

### CSS Específico
- Arquivo: `app/sunmi.css`
- Otimizado para 480x800
- Botões grandes para touch
- Texto legível em tela pequena
- Layout responsivo

### Componentes Adaptados
- Navegação inferior fixa
- Cards compactos
- Formulários simplificados
- Grid responsivo

## 🔧 Configurações Específicas

### Capacitor Config
```typescript
android: {
  minSdkVersion: 23,
  compileSdkVersion: 28,
  targetSdkVersion: 23,
  buildOptions: {
    releaseType: 'APK'
  }
}
```

### Next.js Config
```javascript
webpack: (config) => {
  // Otimizações para pouca RAM
  config.optimization.splitChunks.cacheGroups.vendor.maxSize = 200000
}
```

## 📱 Funcionalidades Suportadas

### ✅ Funcionais
- Sistema de apostas
- Autenticação de usuários
- Navegação entre páginas
- Formulários de aposta
- Visualização de partidas
- Histórico de transações

### ⚠️ Limitações
- Sem notificações push (Android 6.0)
- Sem recursos avançados de câmera
- Performance limitada em animações
- Armazenamento limitado

## 🐛 Troubleshooting

### Erro de Instalação
```bash
# Verificar se APK é compatível
aapt dump badging app-debug.apk | grep sdkVersion

# Verificar espaço disponível
adb shell df /data
```

### Performance Lenta
- Reduzir animações CSS
- Limitar número de componentes na tela
- Usar lazy loading
- Otimizar imagens

### Problemas de Layout
- Verificar CSS específico para Sunmi
- Testar em orientação portrait/landscape
- Ajustar tamanhos de fonte

## 📊 Métricas de Performance

### Tamanho do APK
- **Target**: < 50MB
- **Atual**: ~25MB (otimizado)

### Tempo de Carregamento
- **Inicial**: < 3 segundos
- **Navegação**: < 1 segundo

### Uso de RAM
- **Máximo**: 512MB
- **Típico**: 256MB

## 🔄 Atualizações

### Via APK
1. Gerar novo APK com `./build-sunmi.sh`
2. Instalar sobre versão anterior
3. Dados preservados

### Via Web (Fallback)
- Acesso via browser: https://pos.mmapay.pro
- Funcionalidade completa
- Sem instalação necessária

## 📞 Suporte

Para problemas específicos do Sunmi V1s-G:
- Verificar logs: `adb logcat`
- Testar via browser primeiro
- Contatar suporte técnico

---

**APK otimizado para Sunmi V1s-G - Android 6.0 Marshmallow** 📱⚽
