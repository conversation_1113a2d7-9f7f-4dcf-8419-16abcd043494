#!/bin/bash

# Script completo para configurar e buildar Bolão Brasil para Sunmi V1s-G
echo "🚀 Configuração completa para Sunmi V1s-G iniciada..."

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERRO]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[AVISO]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# 1. Verificar e instalar dependências
log "Verificando dependências..."

# Node.js
if ! command -v node &> /dev/null; then
    error "Node.js não encontrado!"
    exit 1
fi

# NPM
if ! command -v npm &> /dev/null; then
    error "NPM não encontrado!"
    exit 1
fi

log "Node.js e NPM encontrados ✅"

# 2. Instalar dependências do projeto
log "Instalando dependências do projeto..."
npm install --silent

# 3. Configurar Android SDK automaticamente
if [ -z "$ANDROID_HOME" ]; then
    warning "Android SDK não configurado. Configurando automaticamente..."
    chmod +x setup-android-auto.sh
    ./setup-android-auto.sh
    source ~/.bashrc
    export ANDROID_HOME="/opt/android-sdk"
    export PATH="$PATH:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools"
fi

# 4. Verificar Java
if ! java -version 2>&1 | grep -q "1.8\|11"; then
    warning "Java não encontrado. Instalando OpenJDK 8..."
    if command -v apt &> /dev/null; then
        sudo apt update && sudo apt install -y openjdk-8-jdk
    elif command -v yum &> /dev/null; then
        sudo yum install -y java-1.8.0-openjdk-devel
    elif command -v brew &> /dev/null; then
        brew install openjdk@8
    fi
    export JAVA_HOME="/usr/lib/jvm/java-8-openjdk-amd64"
fi

log "Java configurado ✅"

# 5. Criar diretórios necessários
log "Criando diretórios..."
mkdir -p public/downloads
mkdir -p public/icons
mkdir -p android/app/src/main/res/values

# 6. Build do Next.js
log "Fazendo build do Next.js..."
npm run build

if [ $? -ne 0 ]; then
    error "Erro no build do Next.js!"
    exit 1
fi

log "Build do Next.js concluído ✅"

# 7. Sincronizar Capacitor
log "Sincronizando Capacitor..."
npx cap sync android

if [ $? -ne 0 ]; then
    error "Erro na sincronização do Capacitor!"
    exit 1
fi

log "Capacitor sincronizado ✅"

# 8. Configurar Android
log "Configurando Android para Sunmi V1s-G..."

# Configurar local.properties
cat > android/local.properties << EOF
sdk.dir=${ANDROID_HOME:-/opt/android-sdk}
android.useAndroidX=true
android.enableJetifier=true
org.gradle.jvmargs=-Xmx1024m -XX:MaxPermSize=256m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.parallel=false
org.gradle.configureondemand=false
org.gradle.daemon=false
EOF

# 9. Build do APK
log "Fazendo build do APK..."
cd android

# Dar permissão ao gradlew
chmod +x gradlew

# Limpar builds anteriores
./gradlew clean

# Build debug
./gradlew assembleDebug --stacktrace --info

BUILD_STATUS=$?
cd ..

# 10. Verificar resultado
APK_PATH="android/app/build/outputs/apk/debug/app-debug.apk"

if [ $BUILD_STATUS -eq 0 ] && [ -f "$APK_PATH" ]; then
    log "APK criado com sucesso! ✅"
    
    # Copiar APK para pasta pública
    cp "$APK_PATH" public/downloads/bolao-brasil-sunmi.apk
    
    # Informações do APK
    APK_SIZE=$(ls -lh "$APK_PATH" | awk '{print $5}')
    info "Tamanho do APK: $APK_SIZE"
    
    # Gerar QR Code se possível
    if command -v qrencode &> /dev/null; then
        log "Gerando QR Code..."
        qrencode -t PNG -o public/downloads/qr-code.png "https://pos.mmapay.pro/downloads/bolao-brasil-sunmi.apk"
        log "QR Code gerado ✅"
    else
        warning "qrencode não encontrado. QR Code não gerado."
    fi
    
    echo ""
    echo "🎉 BUILD CONCLUÍDO COM SUCESSO!"
    echo ""
    echo "📱 APK para Sunmi V1s-G:"
    echo "   📍 Local: $APK_PATH"
    echo "   🌐 Web: https://pos.mmapay.pro/downloads/bolao-brasil-sunmi.apk"
    echo "   📊 Tamanho: $APK_SIZE"
    echo ""
    echo "✅ Compatibilidade:"
    echo "   📱 Sunmi V1s-G"
    echo "   🤖 Android 6.0 Marshmallow (API 23)"
    echo "   🏗️ ARM 32-bit (armeabi-v7a)"
    echo "   📺 Resolução 480x800"
    echo "   💾 RAM 1GB"
    echo ""
    echo "📲 Como instalar:"
    echo "   1. Via ADB: adb install $APK_PATH"
    echo "   2. Via Web: Acesse https://pos.mmapay.pro e clique no botão flutuante"
    echo "   3. Via QR Code: Escaneie o código na página"
    echo ""
    echo "🌐 Botão flutuante de download adicionado ao site!"
    echo "🖨️ Sistema de impressão térmica Sunmi configurado!"
    echo "📋 Bilhetes com logos dos times implementados!"
    echo "🔄 Reinicie o servidor: npm run dev"
    
else
    error "Falha ao criar APK!"
    echo ""
    echo "🔧 Possíveis soluções:"
    echo "   1. Verificar se Android SDK está instalado: echo \$ANDROID_HOME"
    echo "   2. Verificar se Java está instalado: java -version"
    echo "   3. Verificar logs: cat android/app/build/outputs/logs/gradle-debug.log"
    echo "   4. Tentar novamente: ./build-complete.sh"
    exit 1
fi

# 11. Iniciar servidor de desenvolvimento
read -p "🚀 Deseja iniciar o servidor de desenvolvimento? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log "Iniciando servidor..."
    npm run dev
fi

log "Script concluído!"
