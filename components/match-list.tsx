"use client"
import { useState } from "react"
import { usePartidas } from "@/hooks/use-partidas"
import { MatchCard } from "@/components/match-card"
import { Loader2, ShoppingCart } from "lucide-react"
import { useBetSlip } from "@/hooks/use-bet-slip"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"

export function MatchList() {
  const { partidas, loading, error } = usePartidas()
  const { bets, hasRequiredSelections, requiredSelections } = useBetSlip()
  const router = useRouter()
  const [isProcessing, setIsProcessing] = useState(false)

  const handleConfirmBets = () => {
    if (!hasRequiredSelections()) return

    setIsProcessing(true)

    // Simular processamento
    setTimeout(() => {
      router.push("/bet-slip")
      setIsProcessing(false)
    }, 800)
  }

  if (loading) {
    return (
      <div className="p-4 flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-green-500" />
        <span className="ml-2 text-gray-300">Carregando partidas...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-4 text-center">
        <p className="text-red-400">{error}</p>
      </div>
    )
  }

  if (partidas.length === 0) {
    return (
      <div className="p-4 text-center">
        <p className="text-gray-400">Nenhuma partida disponível no momento.</p>
      </div>
    )
  }

  return (
    <div className="p-2 pb-24 relative">
      <div className="bg-gradient-to-r from-green-600 to-green-700 text-center py-2 rounded-t-lg mb-1">
        <h1 className="text-lg font-bold text-white">Jogos de Hoje</h1>
      </div>

      <div className="space-y-1">
        {partidas.map((partida) => (
          <MatchCard key={partida.partida_id} partida={partida} />
        ))}
      </div>

      {/* Contador de seleções e botão de confirmação */}
      <div className="fixed bottom-0 left-0 right-0 bg-gradient-to-r from-gray-900 to-gray-950 p-4 border-t border-gray-800 z-10">
        <div className="max-w-md mx-auto">
          <div className="flex justify-between items-center mb-3">
            <div className="text-sm text-gray-300">
              Seleções:{" "}
              <span className={bets.length === requiredSelections ? "text-green-400" : "text-yellow-400"}>
                {bets.length}/{requiredSelections}
              </span>
            </div>
            <div className="text-xs text-gray-400">
              {bets.length < requiredSelections
                ? `Selecione mais ${requiredSelections - bets.length} times`
                : "Todas as seleções feitas!"}
            </div>
          </div>

          <Button
            onClick={handleConfirmBets}
            disabled={!hasRequiredSelections() || isProcessing}
            className="w-full bg-green-600 hover:bg-green-700 flex items-center justify-center gap-2 h-12 text-base"
          >
            {isProcessing ? <Loader2 className="h-5 w-5 animate-spin" /> : <ShoppingCart className="h-5 w-5" />}
            {isProcessing ? "Processando..." : "Confirmar Apostas"}
          </Button>
        </div>
      </div>
    </div>
  )
}
