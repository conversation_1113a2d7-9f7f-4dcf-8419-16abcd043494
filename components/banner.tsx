"use client"

import { useState, useEffect } from "react"

interface BannerProps {
  className?: string
}

export function Banner({ className = "" }: BannerProps) {
  const [bannerText, setBannerText] = useState("Jogos de Hoje")
  const [bannerColor, setBannerColor] = useState("bg-green-600")
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    // Carregar configuracoes do banner do localStorage ou API
    const savedBannerText = localStorage.getItem("banner-text")
    const savedBannerColor = localStorage.getItem("banner-color")
    const savedVisibility = localStorage.getItem("banner-visible")

    if (savedBannerText) {
      setBannerText(savedBannerText)
    }

    if (savedBannerColor) {
      setBannerColor(savedBannerColor)
    }

    if (savedVisibility !== null) {
      setIsVisible(savedVisibility === "true")
    }
  }, [])

  if (!isVisible) {
    return null
  }

  return (
    <div className={`${bannerColor} text-white text-center py-3 px-4 font-semibold text-lg ${className}`}>
      {bannerText}
    </div>
  )
}
