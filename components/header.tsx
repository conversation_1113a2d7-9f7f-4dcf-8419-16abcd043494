"use client"

import { <PERSON>u, <PERSON>Cart } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useBetSlip } from "@/hooks/use-bet-slip"
import { Logo } from "@/components/logo"

interface HeaderProps {
  onMenuClick: () => void
  onBetSlipClick: () => void
}

export function Header({ onMenuClick, onBetSlipClick }: HeaderProps) {
  const { bets, totalValue } = useBetSlip()

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-blue-900 to-indigo-900 shadow-lg">
      <div className="flex items-center justify-between p-4">
        <Button variant="ghost" size="sm" onClick={onMenuClick} className="text-white hover:bg-white/10">
          <Menu className="h-5 w-5" />
        </Button>

        <div className="flex items-center space-x-2">
          <Logo size="md" />
        </div>

        <div className="flex items-center space-x-2">
          <div className="text-right">
            <div className="text-xs text-gray-300">Saldo</div>
            <div className="text-sm font-semibold text-green-400">R$ 990,00</div>
          </div>

          <Button variant="default" size="sm" className="bg-green-600 hover:bg-green-700">
            Depósito
          </Button>

          <Button variant="ghost" size="sm" onClick={onBetSlipClick} className="relative text-white hover:bg-white/10">
            <ShoppingCart className="h-5 w-5" />
            {bets.length > 0 && (
              <Badge className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center bg-red-500">
                {bets.length}
              </Badge>
            )}
          </Button>
        </div>
      </div>
    </header>
  )
}
