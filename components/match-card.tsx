"use client"

import { useState } from "react"
import { useBetSlip } from "@/hooks/use-bet-slip"
import type { Partida } from "@/types/api-futebol"
import Image from "next/image"

interface MatchCardProps {
  partida: Partida
}

export function MatchCard({ partida }: MatchCardProps) {
  const { addBet, removeBet, isBetSelected } = useBetSlip()
  const [selectedBet, setSelectedBet] = useState<string | null>(null)

  // Gera odds aleatorias mas consistentes para demonstracao
  const generateOdds = (partidaId: number) => {
    const seed = partidaId * 123 // Multiplicador para mais variacao
    const random1 = (seed % 100) / 100
    const random2 = ((seed * 7) % 100) / 100
    const random3 = ((seed * 13) % 100) / 100

    return {
      home: Number((1.5 + random1 * 1.5).toFixed(2)), // 1.5 a 3.0
      draw: Number((2.8 + random2 * 1.0).toFixed(2)), // 2.8 a 3.8
      away: Number((1.8 + random3 * 2.5).toFixed(2)), // 1.8 a 4.3
    }
  }

  const odds = generateOdds(partida.partida_id)

  // Funcao para formatar a data no estilo brasileiro
  const formatDate = (dateStr: string, timeStr: string) => {
    try {
      const [year, month, day] = dateStr.split('-')
      const months = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
                     'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez']
      const monthName = months[parseInt(month) - 1]
      const [hours, minutes] = timeStr.split(':')

      return `${day.padStart(2, '0')}/${monthName} - ${hours}:${minutes}`
    } catch {
      return `${dateStr} - ${timeStr}`
    }
  }

  const handleBetClick = (betType: string, team: string, oddValue: number) => {
    const betId = `${partida.partida_id}-${betType}`

    if (selectedBet === betId) {
      setSelectedBet(null)
      removeBet(betId)
    } else {
      setSelectedBet(betId)
      const bet = {
        id: betId,
        matchId: partida.partida_id,
        betType,
        team,
        odd: oddValue,
        match: {
          id: partida.partida_id,
          homeTeam: partida.time_mandante.nome_popular,
          homeTeamLogo: partida.time_mandante.escudo,
          awayTeam: partida.time_visitante.nome_popular,
          awayTeamLogo: partida.time_visitante.escudo,
          league: partida.campeonato.nome,
          date: formatDate(partida.data_realizacao, partida.hora_realizacao),
        },
      }
      addBet(bet)
    }
  }

  return (
    <div className="bg-gray-900 p-3 mb-1 border-b border-gray-700">
      <div className="flex items-center justify-between">
        {/* Secao dos times a esquerda */}
        <div className="flex flex-col gap-1 flex-1">
          {/* Time mandante */}
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 bg-white rounded-full flex items-center justify-center overflow-hidden">
              <Image
                src={partida.time_mandante.escudo || "/placeholder.svg"}
                alt={partida.time_mandante.nome_popular}
                width={16}
                height={16}
                className="object-contain"
              />
            </div>
            <span className="text-white text-sm font-medium">
              {partida.time_mandante.nome_popular}
            </span>
          </div>

          {/* Time visitante */}
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 bg-white rounded-full flex items-center justify-center overflow-hidden">
              <Image
                src={partida.time_visitante.escudo || "/placeholder.svg"}
                alt={partida.time_visitante.nome_popular}
                width={16}
                height={16}
                className="object-contain"
              />
            </div>
            <span className="text-white text-sm font-medium">
              {partida.time_visitante.nome_popular}
            </span>
          </div>

          {/* Data e hora */}
          <div className="mt-1">
            <div className="text-xs text-gray-400">
              {formatDate(partida.data_realizacao, partida.hora_realizacao)}
            </div>
          </div>
        </div>

        {/* Botoes de odds a direita - formato circular */}
        <div className="flex gap-1">
          {/* Odd Casa (1) - com escudo do time mandante */}
          <button
            onClick={() => handleBetClick("home", partida.time_mandante.nome_popular, odds.home)}
            className={`w-12 h-12 rounded-full flex items-center justify-center ${
              selectedBet === `${partida.partida_id}-home`
                ? "bg-green-600 hover:bg-green-700"
                : "bg-gray-700 hover:bg-gray-600"
            } transition-colors relative`}
          >
            <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center overflow-hidden">
              <Image
                src={partida.time_mandante.escudo || "/placeholder.svg"}
                alt={partida.time_mandante.nome_popular}
                width={20}
                height={20}
                className="object-contain"
              />
            </div>
          </button>

          {/* Odd Empate (X) */}
          <button
            onClick={() => handleBetClick("draw", "Empate", odds.draw)}
            className={`w-12 h-12 rounded-full flex items-center justify-center ${
              selectedBet === `${partida.partida_id}-draw`
                ? "bg-green-600 hover:bg-green-700"
                : "bg-gray-700 hover:bg-gray-600"
            } transition-colors`}
          >
            <span className="text-white font-bold text-lg">X</span>
          </button>

          {/* Odd Fora (2) - com escudo do time visitante */}
          <button
            onClick={() => handleBetClick("away", partida.time_visitante.nome_popular, odds.away)}
            className={`w-12 h-12 rounded-full flex items-center justify-center ${
              selectedBet === `${partida.partida_id}-away`
                ? "bg-green-600 hover:bg-green-700"
                : "bg-gray-700 hover:bg-gray-600"
            } transition-colors relative`}
          >
            <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center overflow-hidden">
              <Image
                src={partida.time_visitante.escudo || "/placeholder.svg"}
                alt={partida.time_visitante.nome_popular}
                width={20}
                height={20}
                className="object-contain"
              />
            </div>
          </button>
        </div>
      </div>
    </div>
  )
}
