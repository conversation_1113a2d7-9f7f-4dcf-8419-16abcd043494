"use client"

import { useState } from "react"
import { useBetSlip } from "@/hooks/use-bet-slip"
import type { Partida } from "@/types/api-futebol"
import Image from "next/image"

interface MatchCardProps {
  partida: Partida
}

export function MatchCard({ partida }: MatchCardProps) {
  const { addBet, removeBet, isBetSelected } = useBetSlip()
  const [selectedBet, setSelectedBet] = useState<string | null>(null)

  // Gera odds aleatórias mas consistentes para demonstração
  const generateOdds = (partidaId: number) => {
    const seed = partidaId * 123 // Multiplicador para mais variação
    const random1 = (seed % 100) / 100
    const random2 = ((seed * 7) % 100) / 100
    const random3 = ((seed * 13) % 100) / 100

    return {
      home: Number((1.5 + random1 * 1.5).toFixed(2)), // 1.5 a 3.0
      draw: Number((2.8 + random2 * 1.0).toFixed(2)), // 2.8 a 3.8
      away: Number((1.8 + random3 * 2.5).toFixed(2)), // 1.8 a 4.3
    }
  }

  const odds = generateOdds(partida.partida_id)

  const handleBetClick = (betType: string, team: string) => {
    const betId = `${partida.partida_id}-${betType}`

    if (selectedBet === betId) {
      setSelectedBet(null)
      removeBet(betId)
    } else {
      setSelectedBet(betId)
      const bet = {
        id: betId,
        matchId: partida.partida_id,
        betType,
        team,
        match: {
          id: partida.partida_id,
          homeTeam: partida.time_mandante.nome_popular,
          homeTeamLogo: partida.time_mandante.escudo,
          awayTeam: partida.time_visitante.nome_popular,
          awayTeamLogo: partida.time_visitante.escudo,
          league: partida.campeonato.nome,
          date: `${partida.data_realizacao} - ${partida.hora_realizacao}`,
        },
      }
      addBet(bet)
    }
  }

  return (
    <div className="bg-black p-3 mb-1 rounded-sm">
      {/* Times e informações */}
      <div className="flex justify-between items-center mb-2">
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center overflow-hidden">
              <Image
                src={partida.time_mandante.escudo || "/placeholder.svg"}
                alt={partida.time_mandante.nome_popular}
                width={25}
                height={25}
                className="object-contain"
              />
            </div>
            <span className="text-white text-sm">{partida.time_mandante.nome_popular}</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center overflow-hidden">
              <Image
                src={partida.time_visitante.escudo || "/placeholder.svg"}
                alt={partida.time_visitante.nome_popular}
                width={25}
                height={25}
                className="object-contain"
              />
            </div>
            <span className="text-white text-sm">{partida.time_visitante.nome_popular}</span>
          </div>
        </div>

        {/* Botões de aposta */}
        <div className="flex gap-1">
          {/* Casa */}
          <button
            onClick={() => handleBetClick("home", partida.time_mandante.nome_popular)}
            className={`w-12 h-12 rounded-md flex flex-col items-center justify-center ${
              selectedBet === `${partida.partida_id}-home` ? "bg-green-600" : "bg-white"
            }`}
          >
            <div className="w-7 h-7 flex items-center justify-center">
              <Image
                src={partida.time_mandante.escudo || "/placeholder.svg"}
                alt={partida.time_mandante.nome_popular}
                width={27}
                height={27}
                className="object-contain"
              />
            </div>
          </button>

          {/* Empate */}
          <button
            onClick={() => handleBetClick("draw", "Empate")}
            className={`w-12 h-12 rounded-md flex items-center justify-center ${
              selectedBet === `${partida.partida_id}-draw` ? "bg-green-600" : "bg-white"
            }`}
          >
            <div className="w-6 h-6 flex items-center justify-center">
              <span className="text-black font-bold">X</span>
            </div>
          </button>

          {/* Fora */}
          <button
            onClick={() => handleBetClick("away", partida.time_visitante.nome_popular)}
            className={`w-12 h-12 rounded-md flex flex-col items-center justify-center ${
              selectedBet === `${partida.partida_id}-away` ? "bg-green-600" : "bg-white"
            }`}
          >
            <div className="w-7 h-7 flex items-center justify-center">
              <Image
                src={partida.time_visitante.escudo || "/placeholder.svg"}
                alt={partida.time_visitante.nome_popular}
                width={27}
                height={27}
                className="object-contain"
              />
            </div>
          </button>
        </div>
      </div>

      {/* Data e hora */}
      <div className="text-center">
        <span className="text-xs text-gray-300">{`${partida.data_realizacao} - ${partida.hora_realizacao}`}</span>
      </div>

      {/* Estádio */}
      {partida.estadio && (
        <div className="text-center">
          <span className="text-xs text-gray-400">{partida.estadio.nome_popular}</span>
        </div>
      )}
    </div>
  )
}
