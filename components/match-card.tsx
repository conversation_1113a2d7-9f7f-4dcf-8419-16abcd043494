"use client"

import { useState } from "react"
import { useBetSlip } from "@/hooks/use-bet-slip"
import type { Partida } from "@/types/api-futebol"
import Image from "next/image"

interface MatchCardProps {
  partida: Partida
}

export function MatchCard({ partida }: MatchCardProps) {
  const { addBet, removeBet, isBetSelected } = useBetSlip()
  const [selectedBet, setSelectedBet] = useState<string | null>(null)

  // Gera odds aleatorias mas consistentes para demonstracao
  const generateOdds = (partidaId: number) => {
    const seed = partidaId * 123 // Multiplicador para mais variacao
    const random1 = (seed % 100) / 100
    const random2 = ((seed * 7) % 100) / 100
    const random3 = ((seed * 13) % 100) / 100

    return {
      home: Number((1.5 + random1 * 1.5).toFixed(2)), // 1.5 a 3.0
      draw: Number((2.8 + random2 * 1.0).toFixed(2)), // 2.8 a 3.8
      away: Number((1.8 + random3 * 2.5).toFixed(2)), // 1.8 a 4.3
    }
  }

  const odds = generateOdds(partida.partida_id)

  // Funcao para formatar a data no estilo brasileiro
  const formatDate = (dateStr: string, timeStr: string) => {
    try {
      const [year, month, day] = dateStr.split('-')
      const months = ['jan', 'fev', 'mar', 'abr', 'mai', 'jun',
                     'jul', 'ago', 'set', 'out', 'nov', 'dez']
      const monthName = months[parseInt(month) - 1]
      const [hours, minutes] = timeStr.split(':')

      // Simula um numero aleatorio para o "+486" baseado no ID da partida
      const randomNum = (partida.partida_id * 17) % 999 + 100

      return `${day}/${monthName} ${hours}:${minutes} +${randomNum}`
    } catch {
      return `${dateStr} ${timeStr}`
    }
  }

  const handleBetClick = (betType: string, team: string, oddValue: number) => {
    const betId = `${partida.partida_id}-${betType}`

    if (selectedBet === betId) {
      setSelectedBet(null)
      removeBet(betId)
    } else {
      setSelectedBet(betId)
      const bet = {
        id: betId,
        matchId: partida.partida_id,
        betType,
        team,
        odd: oddValue,
        match: {
          id: partida.partida_id,
          homeTeam: partida.time_mandante.nome_popular,
          homeTeamLogo: partida.time_mandante.escudo,
          awayTeam: partida.time_visitante.nome_popular,
          awayTeamLogo: partida.time_visitante.escudo,
          league: partida.campeonato.nome,
          date: formatDate(partida.data_realizacao, partida.hora_realizacao),
        },
      }
      addBet(bet)
    }
  }

  return (
    <div className="bg-gray-900 p-3 mb-2 rounded-lg border border-gray-700">
      <div className="flex justify-between items-center">
        {/* Secao dos times a esquerda */}
        <div className="flex items-center space-x-3">
          {/* Escudos e nomes dos times */}
          <div className="space-y-2">
            {/* Time mandante */}
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center overflow-hidden">
                <Image
                  src={partida.time_mandante.escudo || "/placeholder.svg"}
                  alt={partida.time_mandante.nome_popular}
                  width={20}
                  height={20}
                  className="object-contain"
                />
              </div>
              <span className="text-white text-sm font-medium">
                {partida.time_mandante.nome_popular}
              </span>
            </div>

            {/* Time visitante */}
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center overflow-hidden">
                <Image
                  src={partida.time_visitante.escudo || "/placeholder.svg"}
                  alt={partida.time_visitante.nome_popular}
                  width={20}
                  height={20}
                  className="object-contain"
                />
              </div>
              <span className="text-white text-sm font-medium">
                {partida.time_visitante.nome_popular}
              </span>
            </div>
          </div>

          {/* Data e hora */}
          <div className="ml-4">
            <div className="text-xs text-gray-400">
              {formatDate(partida.data_realizacao, partida.hora_realizacao)}
            </div>
          </div>
        </div>

        {/* Botoes de odds a direita */}
        <div className="flex gap-2">
          {/* Odd Casa (1) */}
          <button
            onClick={() => handleBetClick("home", partida.time_mandante.nome_popular, odds.home)}
            className={`px-4 py-2 rounded-md text-white font-semibold text-sm min-w-[50px] ${
              selectedBet === `${partida.partida_id}-home`
                ? "bg-green-600 hover:bg-green-700"
                : "bg-gray-700 hover:bg-gray-600"
            } transition-colors`}
          >
            {odds.home.toFixed(2).replace('.', ',')}
          </button>

          {/* Odd Empate (X) */}
          <button
            onClick={() => handleBetClick("draw", "Empate", odds.draw)}
            className={`px-4 py-2 rounded-md text-white font-semibold text-sm min-w-[50px] ${
              selectedBet === `${partida.partida_id}-draw`
                ? "bg-green-600 hover:bg-green-700"
                : "bg-gray-700 hover:bg-gray-600"
            } transition-colors`}
          >
            {odds.draw.toFixed(2).replace('.', ',')}
          </button>

          {/* Odd Fora (2) */}
          <button
            onClick={() => handleBetClick("away", partida.time_visitante.nome_popular, odds.away)}
            className={`px-4 py-2 rounded-md text-white font-semibold text-sm min-w-[50px] ${
              selectedBet === `${partida.partida_id}-away`
                ? "bg-green-600 hover:bg-green-700"
                : "bg-gray-700 hover:bg-gray-600"
            } transition-colors`}
          >
            {odds.away.toFixed(2).replace('.', ',')}
          </button>
        </div>
      </div>
    </div>
  )
}
