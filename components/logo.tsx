interface LogoProps {
  size?: "sm" | "md" | "lg"
  className?: string
}

export function Logo({ size = "md", className = "" }: LogoProps) {
  const sizes = {
    sm: { width: 80, height: 32 },
    md: { width: 120, height: 48 },
    lg: { width: 160, height: 64 },
  }

  const currentSize = sizes[size]

  return (
    <div className={`flex items-center ${className}`}>
      <div
        className="relative bg-gradient-to-r from-green-500 to-blue-600 rounded-lg p-2 flex items-center justify-center shadow-lg"
        style={{ width: currentSize.width, height: currentSize.height }}
      >
        <div className="text-white font-bold text-center">
          <div className="text-xs leading-tight">BOLÃO</div>
          <div className="text-lg leading-tight">⚽</div>
          <div className="text-xs leading-tight">BRASIL</div>
        </div>
      </div>
    </div>
  )
}
