import { Card } from "@/components/ui/card"

export function LoadingSkeleton() {
  return (
    <div className="p-4 space-y-4">
      {/* Header Skeleton */}
      <div className="bg-gradient-to-r from-gray-600 to-gray-700 text-center py-3 rounded-lg animate-pulse">
        <div className="h-6 bg-gray-500 rounded w-20 mx-auto"></div>
      </div>

      {/* League Skeleton */}
      <div className="space-y-0.5">
        <div className="bg-gradient-to-r from-gray-600 to-gray-700 px-4 py-2 rounded-t-lg animate-pulse">
          <div className="flex justify-between items-center">
            <div className="h-4 bg-gray-500 rounded w-16"></div>
            <div className="flex space-x-8">
              <div className="h-3 bg-gray-500 rounded w-8"></div>
              <div className="h-3 bg-gray-500 rounded w-8"></div>
              <div className="h-3 bg-gray-500 rounded w-8"></div>
            </div>
          </div>
        </div>

        {/* Match Skeletons */}
        {[1, 2, 3].map((i) => (
          <Card key={i} className="bg-gray-800 border-gray-700 rounded-none animate-pulse">
            <div className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1 space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-gray-600 rounded-full"></div>
                    <div className="h-4 bg-gray-600 rounded w-24"></div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-gray-600 rounded-full"></div>
                    <div className="h-4 bg-gray-600 rounded w-24"></div>
                  </div>
                  <div className="ml-9">
                    <div className="h-3 bg-gray-600 rounded w-20"></div>
                  </div>
                </div>
                <div className="flex space-x-2">
                  {[1, 2, 3].map((j) => (
                    <div key={j} className="w-14 h-14 bg-gray-600 rounded-lg"></div>
                  ))}
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
}
