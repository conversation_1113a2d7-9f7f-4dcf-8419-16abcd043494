"use client"

import { useState } from "react"
import { X, Trash2, ShoppingCart } from "lucide-react"
import { <PERSON>ton } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { useBetSlip } from "@/hooks/use-bet-slip"
import { useRouter } from "next/navigation"
import Image from "next/image"

interface BetSlipProps {
  open: boolean
  onClose: () => void
}

export function BetSlip({ open, onClose }: BetSlipProps) {
  const { bets, removeBet, clearBets, setTotalValue } = useBetSlip()
  const [betAmount, setBetAmount] = useState("")
  const router = useRouter()

  const isValidBetAmount = betAmount && !isNaN(Number.parseFloat(betAmount)) && Number.parseFloat(betAmount) > 0

  const handlePlaceBet = () => {
    if (bets.length === 0 || !betAmount) return

    // Simulate bet placement
    const betData = {
      id: `BT${Date.now()}`,
      bets: bets.map((bet) => ({
        ...bet,
        match: {
          ...bet.match,
          date: bet.match.date || new Date().toLocaleDateString("pt-BR"),
        },
      })),
      amount: Number.parseFloat(betAmount),
      status: "PENDENTE",
      date: new Date().toLocaleString("pt-BR"),
    }

    // Store bet in localStorage for demo
    const existingBets = JSON.parse(localStorage.getItem("userBets") || "[]")
    existingBets.push(betData)
    localStorage.setItem("userBets", JSON.stringify(existingBets))

    // Store the bet amount for the ticket
    setTotalValue(Number.parseFloat(betAmount))

    // Show success message
    alert(`Bilhete ${betData.id} criado com sucesso!`)

    clearBets()
    setBetAmount("")
    onClose()

    // Navigate to tickets page
    router.push("/bilhetes")
  }

  return (
    <div className={`bet-slip glass-effect ${open ? "open" : ""}`}>
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between p-4 border-b border-white/20">
          <h2 className="text-lg font-semibold">Cupom de Apostas</h2>
          <Button variant="ghost" size="sm" onClick={onClose} className="text-white hover:bg-white/10">
            <X className="h-5 w-5" />
          </Button>
        </div>

        <div className="flex-1 overflow-y-auto p-4">
          {bets.length === 0 ? (
            <div className="text-center text-gray-400 mt-8">
              <p>Nenhuma aposta selecionada</p>
              <p className="text-sm mt-2">Clique nos times para selecionar</p>
            </div>
          ) : (
            <div className="space-y-3">
              {bets.map((bet) => (
                <Card key={bet.id} className="glass-effect border-white/20 p-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <div className="flex items-center space-x-1">
                          <div className="w-5 h-5 bg-white rounded-full flex items-center justify-center overflow-hidden">
                            <Image
                              src={bet.match.homeTeamLogo || "/placeholder.svg"}
                              alt={bet.match.homeTeam}
                              width={20}
                              height={20}
                              className="object-contain"
                            />
                          </div>
                          <span className="text-xs">x</span>
                          <div className="w-5 h-5 bg-white rounded-full flex items-center justify-center overflow-hidden">
                            <Image
                              src={bet.match.awayTeamLogo || "/placeholder.svg"}
                              alt={bet.match.awayTeam}
                              width={20}
                              height={20}
                              className="object-contain"
                            />
                          </div>
                        </div>
                        <div className="text-sm font-medium">
                          {bet.match.homeTeam} x {bet.match.awayTeam}
                        </div>
                      </div>
                      <div className="text-xs text-green-400 mt-1 font-bold">{bet.team}</div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeBet(bet.id)}
                      className="text-red-400 hover:bg-red-500/10 p-1"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>

        {bets.length > 0 && (
          <div className="p-4 border-t border-white/20 space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Qtd. Apostas:</span>
                <span>{bets.length}</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Valor da Aposta (R$)</label>
              <Input
                type="number"
                value={betAmount}
                onChange={(e) => setBetAmount(e.target.value)}
                placeholder="0,00"
                className="bg-white/10 border-white/20 text-white"
              />
            </div>

            <Button
              onClick={handlePlaceBet}
              disabled={!isValidBetAmount || bets.length === 0}
              className="w-full bg-green-600 hover:bg-green-700 flex items-center justify-center gap-2"
            >
              <ShoppingCart className="h-4 w-4" />
              Confirmar Aposta
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
