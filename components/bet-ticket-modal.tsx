"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, Trophy, Printer, Share2 } from "lucide-react"
import Image from "next/image"

interface BetTicketModalProps {
  bet: any
  user: any
  onClose: () => void
}

export function BetTicketModal({ bet, user, onClose }: BetTicketModalProps) {
  const [isOpen, setIsOpen] = useState(true)
  const [isPrinting, setIsPrinting] = useState(false)
  const ticketRef = useRef<HTMLDivElement>(null)

  const handleClose = () => {
    setIsOpen(false)
    onClose()
  }

  const handlePrint = () => {
    setIsPrinting(true)

    try {
      const printWindow = window.open("", "_blank")
      if (!printWindow) {
        alert("Por favor, permita pop-ups para imprimir o bilhete.")
        setIsPrinting(false)
        return
      }

      if (ticketRef.current) {
        const content = ticketRef.current.innerHTML

        printWindow.document.write(`
          <!DOCTYPE html>
          <html>
          <head>
            <title>Bilhete ${bet.id}</title>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
              @media print {
                @page {
                  size: 80mm 297mm;
                  margin: 0;
                }
                body {
                  margin: 5mm;
                }
              }
              body {
                font-family: 'Courier New', monospace;
                color: black;
                background: white;
                max-width: 80mm;
                margin: 0 auto;
                padding: 5mm;
                font-size: 12px;
                line-height: 1.3;
              }
              .ticket-container {
                border: 2px solid #000;
                padding: 10px;
              }
              .ticket-header {
                text-align: center;
                border-bottom: 1px solid #000;
                padding-bottom: 10px;
                margin-bottom: 10px;
              }
              .logo-container {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 5px;
              }
              .logo-icon {
                margin-right: 5px;
                font-size: 18px;
              }
              .logo-text {
                font-size: 16px;
                font-weight: bold;
              }
              .subtitle {
                font-size: 11px;
                margin: 2px 0;
              }
              .info-section {
                margin: 10px 0;
              }
              .info-row {
                display: flex;
                justify-content: space-between;
                margin: 3px 0;
                font-size: 11px;
              }
              .info-label {
                font-weight: normal;
              }
              .info-value {
                font-weight: bold;
              }
              .status-box {
                border: 1px solid #000;
                padding: 5px;
                text-align: center;
                font-weight: bold;
                margin: 10px 0;
              }
              .section-title {
                text-align: center;
                font-weight: bold;
                border-top: 1px dashed #000;
                border-bottom: 1px dashed #000;
                padding: 5px 0;
                margin: 15px 0 10px;
              }
              .bet-item {
                margin-bottom: 10px;
                padding-bottom: 5px;
                border-bottom: 1px dashed #ccc;
              }
              .match-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 3px;
              }
              .teams {
                display: flex;
                align-items: center;
              }
              .team-logo {
                width: 16px;
                height: 16px;
                border-radius: 50%;
                background: #f0f0f0;
                border: 1px solid #ddd;
                margin-right: 3px;
              }
              .vs {
                margin: 0 3px;
                font-size: 10px;
              }
              .match-date {
                font-size: 9px;
                color: #666;
              }
              .bet-selection {
                font-weight: bold;
                font-size: 11px;
              }
              .totals {
                margin-top: 15px;
                border-top: 1px solid #000;
                padding-top: 10px;
              }
              .total-row {
                display: flex;
                justify-content: space-between;
                margin: 3px 0;
                font-size: 11px;
              }
              .total-row.final {
                font-weight: bold;
                border-top: 1px dashed #000;
                padding-top: 5px;
                margin-top: 5px;
              }
              .qr-section {
                text-align: center;
                margin: 15px 0;
                border-top: 1px dashed #000;
                padding-top: 10px;
              }
              .qr-code {
                width: 80px;
                height: 80px;
                border: 2px solid #000;
                margin: 10px auto;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
                background: #f9f9f9;
              }
              .footer {
                text-align: center;
                font-size: 9px;
                border-top: 1px solid #000;
                padding-top: 10px;
                margin-top: 15px;
                line-height: 1.4;
              }
              .footer-section {
                margin: 5px 0;
              }
              .company-info {
                border-top: 1px dashed #000;
                padding-top: 8px;
                margin-top: 8px;
              }
              img.team-logo {
                display: inline-block;
                vertical-align: middle;
              }
              .print-instructions {
                background: #f0f0f0;
                padding: 10px;
                margin: 10px 0;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-size: 14px;
                display: block;
              }
              @media print {
                .print-instructions {
                  display: none;
                }
                .no-print {
                  display: none;
                }
              }
              .print-button {
                background: #4CAF50;
                color: white;
                border: none;
                padding: 10px 15px;
                font-size: 16px;
                cursor: pointer;
                border-radius: 5px;
                margin: 10px 0;
                display: block;
                width: 100%;
              }
              .team-name {
                font-size: 10px;
                margin: 0 3px;
              }
            </style>
          </head>
          <body>
            <div class="print-instructions no-print">
              <h3>Instruções de Impressão</h3>
              <p>1. Clique no botão "Imprimir Agora" abaixo</p>
              <p>2. Selecione sua impressora térmica ou normal</p>
              <p>3. Para impressoras térmicas, escolha o tamanho 80mm</p>
              <p>4. Para impressoras normais, escolha "Sem margens" se disponível</p>
              <button class="print-button" onclick="window.print(); return false;">Imprimir Agora</button>
            </div>
            
            <div class="ticket-container">
              ${content}
            </div>
            
            <div class="print-instructions no-print">
              <p>Se a impressão não ficar boa, tente ajustar as configurações de impressão:</p>
              <ul>
                <li>Desative "Cabeçalhos e rodapés"</li>
                <li>Escolha "Ajustar à página" ou "Tamanho real"</li>
                <li>Para impressoras térmicas, escolha papel 80mm</li>
              </ul>
              <button class="print-button" onclick="window.print(); return false;">Imprimir Agora</button>
            </div>
            
            <script>
              // Auto-print after 1 second
              setTimeout(function() {
                window.print();
              }, 1000);
            </script>
          </body>
          </html>
        `)

        printWindow.document.close()
      }
    } catch (error) {
      console.error("Erro ao imprimir:", error)
      alert("Ocorreu um erro ao tentar imprimir. Por favor, tente novamente.")
    } finally {
      setIsPrinting(false)
    }
  }

  // Função para salvar como PDF (na verdade é a mesma impressão, mas o usuário pode escolher salvar como PDF)
  const handleSaveAsPDF = () => {
    handlePrint()
  }

  // Função para compartilhar (pode ser implementada posteriormente)
  const handleShare = () => {
    if (navigator.share) {
      navigator
        .share({
          title: `Bilhete ${bet.id} - Bolão Brasil`,
          text: `Minha aposta no Bolão Brasil: ${bet.bets.length} jogos, R$ ${bet.amount.toFixed(2)}`,
          url: window.location.href,
        })
        .catch((err) => {
          console.error("Erro ao compartilhar:", err)
        })
    } else {
      alert("Compartilhamento não suportado neste navegador")
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="max-w-md bg-white text-black p-0 max-h-[90vh] overflow-y-auto">
        <DialogHeader className="p-4 border-b">
          <DialogTitle className="text-center">Bilhete de Aposta</DialogTitle>
        </DialogHeader>

        <div className="p-4" ref={ticketRef}>
          <div className="ticket-header">
            <div className="logo-container">
              <Trophy className="h-5 w-5 mr-2" />
              <span className="logo-text">BOLÃO BRASIL</span>
            </div>
            <div className="subtitle">Sistema de Apostas Esportivas</div>
            <div className="subtitle">www.bolaobrasil.com</div>
          </div>

          <div className="info-section">
            <div className="info-row">
              <span className="info-label">BILHETE:</span>
              <span className="info-value">{bet.id}</span>
            </div>
            <div className="info-row">
              <span className="info-label">Data:</span>
              <span>{bet.date?.split(",")[0] || ""}</span>
            </div>
            <div className="info-row">
              <span className="info-label">Hora:</span>
              <span>{bet.date?.split(",")[1]?.trim() || ""}</span>
            </div>
            <div className="info-row">
              <span className="info-label">Cliente:</span>
              <span>{user?.name || "Admin"}</span>
            </div>
          </div>

          <div className="status-box">STATUS: {bet.status}</div>

          <div className="section-title">APOSTAS SELECIONADAS</div>

          {bet.bets.map((betItem: any, index: number) => (
            <div key={index} className="bet-item">
              <div className="match-info">
                <div className="teams">
                  <Image
                    src={betItem.match.homeTeamLogo || "/placeholder.svg"}
                    alt={betItem.match.homeTeam}
                    width={16}
                    height={16}
                    className="team-logo"
                  />
                  <span className="team-name">{betItem.match.homeTeam}</span>
                  <span className="vs">x</span>
                  <Image
                    src={betItem.match.awayTeamLogo || "/placeholder.svg"}
                    alt={betItem.match.awayTeam}
                    width={16}
                    height={16}
                    className="team-logo"
                  />
                  <span className="team-name">{betItem.match.awayTeam}</span>
                </div>
                <div className="match-date">{betItem.match.date?.split(" - ")[0] || "Data não disponível"}</div>
              </div>
              <div className="bet-selection">{betItem.team}</div>
            </div>
          ))}

          <div className="totals">
            <div className="total-row">
              <span>Qtd. Apostas:</span>
              <span>{bet.bets.length}</span>
            </div>
            <div className="total-row final">
              <span>Valor Apostado:</span>
              <span>R$ {bet.amount.toFixed(2)}</span>
            </div>
          </div>

          <div className="qr-section">
            <div className="qr-code">QR CODE</div>
          </div>

          <div className="footer">
            <div className="footer-section">
              <div>Guarde este bilhete para conferir o resultado</div>
              <div>Válido por 30 dias após o último jogo</div>
            </div>

            <div className="footer-section">
              <div>
                <strong>BOA SORTE!</strong>
              </div>
              <div>Jogue com responsabilidade</div>
              <div>+18 anos</div>
            </div>

            <div className="company-info">
              <div>
                <strong>BOLÃO BRASIL LTDA</strong>
              </div>
              <div>CNPJ: 00.000.000/0001-00</div>
              <div>Tel: (11) 9999-9999</div>
            </div>
          </div>
        </div>

        <div className="flex justify-between p-4 border-t">
          <Button
            variant="outline"
            onClick={handleShare}
            className="flex items-center gap-2"
            disabled={!navigator.share}
          >
            <Share2 className="h-4 w-4" />
            Compartilhar
          </Button>

          <div className="flex gap-2">
            <Button variant="outline" onClick={handleSaveAsPDF} className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Salvar PDF
            </Button>

            <Button variant="default" onClick={handlePrint} className="flex items-center gap-2" disabled={isPrinting}>
              <Printer className="h-4 w-4" />
              {isPrinting ? "Imprimindo..." : "Imprimir"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
