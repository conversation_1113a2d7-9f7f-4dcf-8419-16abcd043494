"use client"

import type React from "react"

import { useEffect } from "react"
import { PWAInstallButton } from "./pwa-install-button"

export function PWAProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Register service worker
    if ("serviceWorker" in navigator) {
      window.addEventListener("load", () => {
        navigator.serviceWorker
          .register("/sw.js")
          .then((registration) => {
            console.log("✅ Service Worker registered successfully:", registration.scope)
          })
          .catch((error) => {
            console.error("❌ Service Worker registration failed:", error)
          })
      })
    }

    // Check for updates to the service worker
    let refreshing = false
    navigator.serviceWorker?.addEventListener("controllerchange", () => {
      if (refreshing) return
      refreshing = true
      console.log("🔄 Service Worker updated, reloading page...")
      window.location.reload()
    })

    // Handle online/offline status
    const handleOnlineStatus = () => {
      const isOnline = navigator.onLine
      document.body.classList.toggle("offline", !isOnline)

      if (isOnline) {
        console.log("🟢 App is online")
      } else {
        console.log("🔴 App is offline")
      }
    }

    window.addEventListener("online", handleOnlineStatus)
    window.addEventListener("offline", handleOnlineStatus)
    handleOnlineStatus() // Initial check

    return () => {
      window.removeEventListener("online", handleOnlineStatus)
      window.removeEventListener("offline", handleOnlineStatus)
    }
  }, [])

  return (
    <>
      {children}
      <PWAInstallButton />
    </>
  )
}
