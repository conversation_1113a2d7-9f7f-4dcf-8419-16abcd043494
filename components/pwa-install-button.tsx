"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Download, X, Smartphone, Check } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>
  userChoice: Promise<{ outcome: "accepted" | "dismissed" }>
}

export function PWAInstallButton() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [showInstallPrompt, setShowInstallPrompt] = useState(false)
  const [showFloatingButton, setShowFloatingButton] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)
  const [isAndroid, setIsAndroid] = useState(false)
  const [isIOS, setIsIOS] = useState(false)
  const [installStatus, setInstallStatus] = useState<"idle" | "installing" | "success" | "error">("idle")
  const installAttempted = useRef(false)

  useEffect(() => {
    // Check if app is already installed
    const checkIfInstalled = () => {
      if (window.matchMedia("(display-mode: standalone)").matches || window.navigator.standalone === true) {
        setIsInstalled(true)
        return true
      }
      return false
    }

    if (checkIfInstalled()) return

    // Detect platform
    const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera || ""
    const isAndroidDevice = /android/i.test(userAgent)
    const isIOSDevice = /iPad|iPhone|iPod/.test(userAgent) && !(window as any).MSStream

    setIsAndroid(isAndroidDevice)
    setIsIOS(isIOSDevice)

    // Check if user has already dismissed the prompts
    const hasUserDismissedPopup = localStorage.getItem("pwa-popup-dismissed")
    const hasUserDismissedFloating = localStorage.getItem("pwa-floating-dismissed")
    const hasUserInstalled = localStorage.getItem("pwa-installed")

    if (hasUserInstalled) {
      setIsInstalled(true)
      return
    }

    // Handle beforeinstallprompt event for Android
    const handler = (e: Event) => {
      e.preventDefault()
      console.log("🔔 Install prompt captured!")
      setDeferredPrompt(e as BeforeInstallPromptEvent)

      // Show popup if not dismissed
      if (!hasUserDismissedPopup) {
        setTimeout(() => {
          setShowInstallPrompt(true)
        }, 2000)
      }

      // Always show floating button if not dismissed
      if (!hasUserDismissedFloating) {
        setShowFloatingButton(true)
      }
    }

    window.addEventListener("beforeinstallprompt", handler)

    // For Android without beforeinstallprompt (Samsung Internet, etc)
    if (isAndroidDevice) {
      // Always show floating button for Android after 3 seconds
      const timer = setTimeout(() => {
        if (!hasUserDismissedFloating && !checkIfInstalled()) {
          console.log("🤖 Android detected, showing floating button")
          setShowFloatingButton(true)
        }
      }, 3000)

      return () => clearTimeout(timer)
    }

    // For iOS, show install options after 3 seconds
    if (isIOSDevice) {
      const timer = setTimeout(() => {
        if (!hasUserDismissedPopup && !checkIfInstalled()) {
          setShowInstallPrompt(true)
        }
        if (!hasUserDismissedFloating && !checkIfInstalled()) {
          setShowFloatingButton(true)
        }
      }, 3000)

      return () => clearTimeout(timer)
    }

    // Check for installation success
    const handleAppInstalled = () => {
      console.log("🎉 App was installed successfully!")
      setIsInstalled(true)
      setShowFloatingButton(false)
      setShowInstallPrompt(false)
      localStorage.setItem("pwa-installed", "true")
    }

    window.addEventListener("appinstalled", handleAppInstalled)

    // Periodically check if app is installed
    const installCheckInterval = setInterval(() => {
      if (checkIfInstalled()) {
        clearInterval(installCheckInterval)
      }
    }, 1000)

    return () => {
      window.removeEventListener("beforeinstallprompt", handler)
      window.removeEventListener("appinstalled", handleAppInstalled)
      clearInterval(installCheckInterval)
    }
  }, [])

  const handleInstall = async () => {
    if (installAttempted.current) return
    installAttempted.current = true

    setInstallStatus("installing")
    console.log("📱 Starting installation process...")

    try {
      if (deferredPrompt) {
        // Standard installation flow
        await deferredPrompt.prompt()
        const { outcome } = await deferredPrompt.userChoice

        if (outcome === "accepted") {
          console.log("✅ Installation accepted!")
          setDeferredPrompt(null)
          setShowInstallPrompt(false)
          setShowFloatingButton(false)
          setIsInstalled(true)
          setInstallStatus("success")
          localStorage.setItem("pwa-installed", "true")
        } else {
          console.log("❌ Installation rejected by user")
          setInstallStatus("idle")
          installAttempted.current = false
        }
      } else if (isAndroid) {
        // Fallback for Android without beforeinstallprompt
        console.log("⚠️ No install prompt available, showing manual instructions")
        setShowInstallPrompt(true)
        setInstallStatus("idle")
      }
    } catch (error) {
      console.error("❌ Install error:", error)
      setInstallStatus("error")
      // Show manual instructions on error
      setShowInstallPrompt(true)
      setTimeout(() => {
        setInstallStatus("idle")
        installAttempted.current = false
      }, 3000)
    }
  }

  const handleDismissPopup = () => {
    setShowInstallPrompt(false)
    localStorage.setItem("pwa-popup-dismissed", "true")
  }

  const handleDismissFloating = () => {
    setShowFloatingButton(false)
    localStorage.setItem("pwa-floating-dismissed", "true")
  }

  const handleFloatingClick = () => {
    if (deferredPrompt) {
      handleInstall()
    } else {
      // Show popup with manual instructions
      setShowInstallPrompt(true)
    }
  }

  // Don't show anything if already installed
  if (isInstalled) return null

  return (
    <>
      {/* Floating Install Button */}
      {showFloatingButton && (
        <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-[9998] animate-bounce">
          <div className="relative">
            {/* Close button for floating */}
            <Button
              size="sm"
              variant="ghost"
              className="absolute -top-2 -right-2 h-6 w-6 p-0 bg-red-500 hover:bg-red-600 text-white rounded-full z-10"
              onClick={handleDismissFloating}
            >
              <X className="h-3 w-3" />
            </Button>

            {/* Floating install button */}
            <Button
              onClick={handleFloatingClick}
              disabled={installStatus === "installing"}
              className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-xl hover:shadow-blue-500/25 transition-all duration-300 px-6 py-5 rounded-full font-semibold text-base min-w-[200px] flex items-center justify-center"
            >
              {installStatus === "installing" ? (
                <>
                  <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Instalando...
                </>
              ) : installStatus === "success" ? (
                <>
                  <Check className="h-5 w-5 mr-2" />
                  Instalado!
                </>
              ) : installStatus === "error" ? (
                "Tentar novamente"
              ) : (
                <>
                  <Download className="h-5 w-5 mr-2" />
                  Instalar App
                </>
              )}
            </Button>
          </div>
        </div>
      )}

      {/* Popup Modal */}
      {showInstallPrompt && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-[9999] flex items-center justify-center p-4">
          <Card className="w-full max-w-sm mx-auto bg-gradient-to-br from-blue-600 to-blue-800 border-blue-500 shadow-2xl animate-in fade-in-0 zoom-in-95 duration-300">
            <CardContent className="p-6 text-center text-white">
              {/* Close Button */}
              <Button
                size="sm"
                variant="ghost"
                className="absolute top-2 right-2 text-white hover:bg-blue-700 h-8 w-8 p-0"
                onClick={handleDismissPopup}
              >
                <X className="h-4 w-4" />
              </Button>

              {/* Icon */}
              <div className="mb-4 flex justify-center">
                <div className="bg-white/20 rounded-full p-4">
                  <Smartphone className="h-8 w-8 text-white" />
                </div>
              </div>

              {/* Title */}
              <h3 className="text-xl font-bold mb-2">Instalar Bolão Brasil</h3>

              {/* Description */}
              <p className="text-blue-100 mb-6 text-sm leading-relaxed">
                {isIOS
                  ? "Adicione à tela inicial para uma experiência completa de app nativo"
                  : "Instale nosso app para acesso rápido, notificações e uso offline"}
              </p>

              {/* Benefits */}
              <div className="text-left mb-6 space-y-2">
                <div className="flex items-center text-sm text-blue-100">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                  Acesso mais rápido
                </div>
                <div className="flex items-center text-sm text-blue-100">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                  Funciona offline
                </div>
                <div className="flex items-center text-sm text-blue-100">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                  Notificações em tempo real
                </div>
              </div>

              {/* Install Instructions for iOS */}
              {isIOS ? (
                <div className="text-xs text-blue-100 mb-4 p-3 bg-blue-700/50 rounded-lg">
                  <p className="mb-2">Para instalar:</p>
                  <p>1. Toque no ícone de compartilhar ⬆️</p>
                  <p>2. Selecione "Adicionar à Tela Inicial"</p>
                  <p>3. Toque em "Adicionar"</p>
                </div>
              ) : !deferredPrompt ? (
                // Manual instructions for Android
                <div className="text-xs text-blue-100 mb-4 p-3 bg-blue-700/50 rounded-lg">
                  <p className="mb-2">Para instalar no Android:</p>
                  <p>1. Toque no menu ⋮ do navegador</p>
                  <p>2. Selecione "Instalar aplicativo" ou "Adicionar à tela inicial"</p>
                  <p>3. Confirme a instalação</p>
                </div>
              ) : (
                /* Install Button for Android/Desktop */
                <Button
                  className="w-full bg-white text-blue-700 hover:bg-blue-50 font-semibold py-3 text-base"
                  onClick={handleInstall}
                  disabled={installStatus === "installing"}
                >
                  {installStatus === "installing" ? (
                    <>
                      <div className="h-4 w-4 border-2 border-blue-700 border-t-transparent rounded-full animate-spin mr-2"></div>
                      Instalando...
                    </>
                  ) : (
                    <>
                      <Download className="h-5 w-5 mr-2" />
                      Instalar App
                    </>
                  )}
                </Button>
              )}

              {/* Skip Button */}
              <Button
                variant="ghost"
                size="sm"
                className="mt-3 text-blue-100 hover:bg-blue-700 text-sm"
                onClick={handleDismissPopup}
              >
                Agora não
              </Button>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  )
}
