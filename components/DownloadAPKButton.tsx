'use client'

import { useState, useEffect } from 'react'
import { Download, Smartphone, QrCode, X } from 'lucide-react'

export default function DownloadAPKButton() {
  const [isVisible, setIsVisible] = useState(false)
  const [showModal, setShowModal] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    // Verificar se está no cliente
    if (typeof window === 'undefined') return

    // Detectar se é mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)

    // Mostrar botão imediatamente quando o site carregar
    setIsVisible(true)

    return () => {
      window.removeEventListener('resize', checkMobile)
    }
  }, [])

  const handleDownload = () => {
    // Download direto do APK
    const link = document.createElement('a')
    link.href = '/downloads/bolao-brasil-sunmi.apk'
    link.download = 'bolao-brasil-sunmi.apk'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleInstallPWA = async () => {
    // Tentar instalar como PWA
    if ('serviceWorker' in navigator) {
      try {
        await navigator.serviceWorker.register('/sw.js')
        alert('App instalado como PWA! Verifique sua tela inicial.')
      } catch (error) {
        console.error('Erro ao instalar PWA:', error)
      }
    }
  }

  if (!isVisible) return null

  return (
    <>
      {/* Botão Flutuante */}
      <div className="fixed bottom-6 right-6 z-50">
        <button
          onClick={() => setShowModal(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white rounded-full p-4 shadow-lg transition-all duration-300 hover:scale-110 animate-pulse"
          title="Baixar App para Sunmi V1s-G"
        >
          <Smartphone className="w-6 h-6" />
        </button>
      </div>

      {/* Modal de Download */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6 relative">
            {/* Botão Fechar */}
            <button
              onClick={() => setShowModal(false)}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
            >
              <X className="w-5 h-5" />
            </button>

            {/* Título */}
            <div className="text-center mb-6">
              <Smartphone className="w-12 h-12 text-blue-600 mx-auto mb-3" />
              <h2 className="text-xl font-bold text-gray-900">
                Bolão Brasil para Sunmi V1s-G
              </h2>
              <p className="text-gray-600 text-sm mt-2">
                Otimizado para Android 6.0 Marshmallow
              </p>
            </div>

            {/* Especificações */}
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-gray-900 mb-2">Compatível com:</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>✅ Sunmi V1s-G</li>
                <li>✅ Android 6.0 Marshmallow</li>
                <li>✅ Arquitetura ARM 32-bit</li>
                <li>✅ Resolução 480x800</li>
                <li>✅ RAM 1GB</li>
              </ul>
            </div>

            {/* Botões de Download */}
            <div className="space-y-3">
              {/* Download APK */}
              <button
                onClick={handleDownload}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg flex items-center justify-center space-x-2 transition-colors"
              >
                <Download className="w-5 h-5" />
                <span>Baixar APK (Recomendado)</span>
              </button>

              {/* PWA Install */}
              {isMobile && (
                <button
                  onClick={handleInstallPWA}
                  className="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg flex items-center justify-center space-x-2 transition-colors"
                >
                  <Smartphone className="w-5 h-5" />
                  <span>Instalar como PWA</span>
                </button>
              )}

              {/* QR Code */}
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-2">Ou escaneie o QR Code:</p>
                <div className="bg-white p-4 rounded-lg border inline-block">
                  <img 
                    src="/downloads/qr-code.png" 
                    alt="QR Code para download"
                    className="w-32 h-32"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none'
                    }}
                  />
                  <QrCode className="w-32 h-32 text-gray-400" />
                </div>
              </div>
            </div>

            {/* Instruções */}
            <div className="mt-6 text-xs text-gray-500">
              <p className="font-semibold mb-1">Como instalar:</p>
              <ol className="list-decimal list-inside space-y-1">
                <li>Baixe o APK</li>
                <li>Ative "Fontes desconhecidas" nas configurações</li>
                <li>Abra o arquivo APK baixado</li>
                <li>Toque em "Instalar"</li>
              </ol>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
