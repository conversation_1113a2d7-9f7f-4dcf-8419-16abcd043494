"use client"

import { useState, useEffect } from "react"

export function BannerSettings() {
  const [bannerText, setBannerText] = useState("Jogos de Hoje")
  const [bannerColor, setBannerColor] = useState("bg-green-600")
  const [isVisible, setIsVisible] = useState(true)

  const colorOptions = [
    { value: "bg-green-600", label: "Verde", preview: "bg-green-600" },
    { value: "bg-blue-600", label: "Azul", preview: "bg-blue-600" },
    { value: "bg-red-600", label: "Vermel<PERSON>", preview: "bg-red-600" },
    { value: "bg-yellow-600", label: "Amarelo", preview: "bg-yellow-600" },
    { value: "bg-purple-600", label: "Roxo", preview: "bg-purple-600" },
    { value: "bg-gray-600", label: "Cinza", preview: "bg-gray-600" },
  ]

  useEffect(() => {
    // Carregar configuracoes salvas
    const savedText = localStorage.getItem("banner-text")
    const savedColor = localStorage.getItem("banner-color")
    const savedVisibility = localStorage.getItem("banner-visible")
    
    if (savedText) setBannerText(savedText)
    if (savedColor) setBannerColor(savedColor)
    if (savedVisibility) setIsVisible(savedVisibility === "true")
  }, [])

  const handleSave = () => {
    localStorage.setItem("banner-text", bannerText)
    localStorage.setItem("banner-color", bannerColor)
    localStorage.setItem("banner-visible", isVisible.toString())
    
    // Recarregar a pagina para aplicar mudancas
    window.location.reload()
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4 text-gray-800">Configurações do Banner</h2>
      
      {/* Preview do Banner */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Preview:
        </label>
        {isVisible && (
          <div className={`${bannerColor} text-white text-center py-3 px-4 font-semibold text-lg rounded`}>
            {bannerText}
          </div>
        )}
        {!isVisible && (
          <div className="text-gray-500 text-center py-3 px-4 border-2 border-dashed border-gray-300 rounded">
            Banner oculto
          </div>
        )}
      </div>

      {/* Texto do Banner */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Texto do Banner:
        </label>
        <input
          type="text"
          value={bannerText}
          onChange={(e) => setBannerText(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Digite o texto do banner"
        />
      </div>

      {/* Cor do Banner */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Cor do Banner:
        </label>
        <div className="grid grid-cols-3 gap-2">
          {colorOptions.map((color) => (
            <button
              key={color.value}
              onClick={() => setBannerColor(color.value)}
              className={`p-3 rounded-md border-2 transition-all ${
                bannerColor === color.value
                  ? "border-blue-500 ring-2 ring-blue-200"
                  : "border-gray-300 hover:border-gray-400"
              }`}
            >
              <div className={`${color.preview} h-6 w-full rounded mb-1`}></div>
              <span className="text-xs text-gray-600">{color.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Visibilidade */}
      <div className="mb-6">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={isVisible}
            onChange={(e) => setIsVisible(e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm font-medium text-gray-700">
            Mostrar banner
          </span>
        </label>
      </div>

      {/* Botao Salvar */}
      <button
        onClick={handleSave}
        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors font-medium"
      >
        Salvar Configurações
      </button>
    </div>
  )
}
