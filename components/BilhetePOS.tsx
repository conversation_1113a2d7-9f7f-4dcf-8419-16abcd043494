'use client'

import { useState } from 'react'
import { Printer, Download, Share2 } from 'lucide-react'

interface Aposta {
  id: string
  time1: string
  time2: string
  logo1: string
  logo2: string
  data: string
  horario: string
  local: string
  tipoAposta: string
  odd: number
  valor: number
}

interface BilheteProps {
  apostas: Aposta[]
  valorTotal: number
  possibleReturn: number
  codigo: string
  data: string
}

export default function BilhetePOS({ apostas, valorTotal, possibleReturn, codigo, data }: BilheteProps) {
  const [isPrinting, setIsPrinting] = useState(false)

  // Função para imprimir na impressora térmica Sunmi
  const imprimirBilhete = async () => {
    setIsPrinting(true)
    
    try {
      // Verificar se está rodando no Sunmi
      if (typeof window !== 'undefined' && (window as any).sunmi) {
        const sunmi = (window as any).sunmi
        
        // Configurar impressora térmica
        await sunmi.printer.init()
        
        // Cabeçalho
        await sunmi.printer.setAlignment(1) // Centro
        await sunmi.printer.setFontSize(24)
        await sunmi.printer.printText('BOLÃO BRASIL\n')
        await sunmi.printer.printText('Sistema de Apostas Esportivas\n')
        await sunmi.printer.printText('--------------------------------\n')
        
        // Informações do bilhete
        await sunmi.printer.setAlignment(0) // Esquerda
        await sunmi.printer.setFontSize(18)
        await sunmi.printer.printText(`Código: ${codigo}\n`)
        await sunmi.printer.printText(`Data: ${data}\n`)
        await sunmi.printer.printText('--------------------------------\n')
        
        // Apostas
        await sunmi.printer.printText('SELEÇÕES:\n\n')
        
        for (const aposta of apostas) {
          await sunmi.printer.setFontSize(16)
          await sunmi.printer.printText(`${aposta.time1} x ${aposta.time2}\n`)
          await sunmi.printer.setFontSize(14)
          await sunmi.printer.printText(`${aposta.data} - ${aposta.horario}\n`)
          await sunmi.printer.printText(`${aposta.local}\n`)
          await sunmi.printer.printText(`${aposta.tipoAposta} - Odd: ${aposta.odd}\n`)
          await sunmi.printer.printText(`Valor: R$ ${aposta.valor.toFixed(2)}\n`)
          await sunmi.printer.printText('--------------------------------\n')
        }
        
        // Totais
        await sunmi.printer.setFontSize(18)
        await sunmi.printer.printText(`VALOR TOTAL: R$ ${valorTotal.toFixed(2)}\n`)
        await sunmi.printer.printText(`RETORNO POSSÍVEL: R$ ${possibleReturn.toFixed(2)}\n`)
        await sunmi.printer.printText('--------------------------------\n')
        
        // Rodapé
        await sunmi.printer.setAlignment(1) // Centro
        await sunmi.printer.setFontSize(12)
        await sunmi.printer.printText('Aposte com responsabilidade\n')
        await sunmi.printer.printText('pos.mmapay.pro\n')
        await sunmi.printer.printText('--------------------------------\n\n\n')
        
        // Finalizar impressão
        await sunmi.printer.lineWrap(3)
        
        alert('Bilhete impresso com sucesso!')
        
      } else {
        // Fallback para impressão web
        window.print()
      }
      
    } catch (error) {
      console.error('Erro ao imprimir:', error)
      alert('Erro ao imprimir bilhete. Tentando impressão alternativa...')
      window.print()
    } finally {
      setIsPrinting(false)
    }
  }

  const baixarBilhete = () => {
    const bilheteHTML = document.getElementById('bilhete-print')?.innerHTML
    if (bilheteHTML) {
      const blob = new Blob([`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Bilhete ${codigo}</title>
          <style>
            body { font-family: monospace; font-size: 12px; margin: 0; padding: 10px; }
            .center { text-align: center; }
            .bold { font-weight: bold; }
            .line { border-bottom: 1px dashed #000; margin: 5px 0; }
          </style>
        </head>
        <body>${bilheteHTML}</body>
        </html>
      `], { type: 'text/html' })
      
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `bilhete-${codigo}.html`
      a.click()
      URL.revokeObjectURL(url)
    }
  }

  const compartilharBilhete = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Bilhete ${codigo} - Bolão Brasil`,
          text: `Confira meu bilhete de apostas! Código: ${codigo}`,
          url: `https://pos.mmapay.pro/bilhete/${codigo}`
        })
      } catch (error) {
        console.error('Erro ao compartilhar:', error)
      }
    } else {
      // Fallback para copiar link
      navigator.clipboard.writeText(`https://pos.mmapay.pro/bilhete/${codigo}`)
      alert('Link do bilhete copiado!')
    }
  }

  return (
    <div className="max-w-md mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
      {/* Bilhete para impressão */}
      <div id="bilhete-print" className="p-4 font-mono text-sm">
        {/* Cabeçalho */}
        <div className="text-center border-b-2 border-dashed border-gray-300 pb-3 mb-3">
          <div className="text-lg font-bold">BOLÃO BRASIL</div>
          <div className="text-xs">Sistema de Apostas Esportivas</div>
          <div className="text-xs mt-1">pos.mmapay.pro</div>
        </div>

        {/* Informações do bilhete */}
        <div className="border-b border-dashed border-gray-300 pb-3 mb-3">
          <div className="flex justify-between">
            <span>Código:</span>
            <span className="font-bold">{codigo}</span>
          </div>
          <div className="flex justify-between">
            <span>Data:</span>
            <span>{data}</span>
          </div>
        </div>

        {/* Seleções */}
        <div className="border-b border-dashed border-gray-300 pb-3 mb-3">
          <div className="font-bold mb-2">SELEÇÕES: {apostas.length}/11</div>
          
          {apostas.map((aposta, index) => (
            <div key={aposta.id} className="mb-3 text-xs">
              <div className="flex items-center justify-between mb-1">
                <div className="flex items-center space-x-2">
                  <img src={aposta.logo1} alt={aposta.time1} className="w-4 h-4" />
                  <span className="font-semibold">{aposta.time1}</span>
                </div>
                <span className="text-gray-500">X</span>
                <div className="flex items-center space-x-2">
                  <span className="font-semibold">{aposta.time2}</span>
                  <img src={aposta.logo2} alt={aposta.time2} className="w-4 h-4" />
                </div>
              </div>
              
              <div className="text-gray-600">
                <div>{aposta.data} - {aposta.horario}</div>
                <div>{aposta.local}</div>
              </div>
              
              <div className="flex justify-between mt-1">
                <span>{aposta.tipoAposta}</span>
                <span className="font-bold">Odd: {aposta.odd}</span>
              </div>
              
              <div className="flex justify-between">
                <span>Valor:</span>
                <span className="font-bold">R$ {aposta.valor.toFixed(2)}</span>
              </div>
              
              {index < apostas.length - 1 && (
                <div className="border-b border-dotted border-gray-200 mt-2"></div>
              )}
            </div>
          ))}
        </div>

        {/* Totais */}
        <div className="border-b border-dashed border-gray-300 pb-3 mb-3">
          <div className="flex justify-between font-bold">
            <span>VALOR TOTAL:</span>
            <span>R$ {valorTotal.toFixed(2)}</span>
          </div>
          <div className="flex justify-between font-bold text-green-600">
            <span>RETORNO POSSÍVEL:</span>
            <span>R$ {possibleReturn.toFixed(2)}</span>
          </div>
        </div>

        {/* Rodapé */}
        <div className="text-center text-xs text-gray-500">
          <div>Aposte com responsabilidade</div>
          <div>Suporte: pos.mmapay.pro</div>
        </div>
      </div>

      {/* Botões de ação */}
      <div className="p-4 bg-gray-50 flex space-x-2">
        <button
          onClick={imprimirBilhete}
          disabled={isPrinting}
          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg flex items-center justify-center space-x-2 disabled:opacity-50"
        >
          <Printer className="w-4 h-4" />
          <span>{isPrinting ? 'Imprimindo...' : 'Imprimir'}</span>
        </button>
        
        <button
          onClick={baixarBilhete}
          className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg flex items-center justify-center space-x-2"
        >
          <Download className="w-4 h-4" />
          <span>Baixar</span>
        </button>
        
        <button
          onClick={compartilharBilhete}
          className="flex-1 bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg flex items-center justify-center space-x-2"
        >
          <Share2 className="w-4 h-4" />
          <span>Compartilhar</span>
        </button>
      </div>
    </div>
  )
}
