"use client"

import { X, Home, User, Receipt, CreditCard, Users, LogOut, WorkflowIcon as Widget } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { Logo } from "@/components/logo"

interface SidebarProps {
  open: boolean
  onClose: () => void
  user: any
}

export function Sidebar({ open, onClose, user }: SidebarProps) {
  const router = useRouter()

  const menuItems = [
    { icon: Home, label: "Apostar", href: "/" },
    { icon: Widget, label: "Widgets", href: "/widgets" },
    { icon: User, label: "Perfil", href: "/perfil" },
    { icon: Receipt, label: "Bilhetes", href: "/bilhetes" },
    { icon: CreditCard, label: "Transações", href: "/transacoes" },
    ...(user?.isAdmin ? [{ icon: Users, label: "Usu<PERSON><PERSON><PERSON>", href: "/admin" }] : []),
  ]

  const handleNavigation = (href: string) => {
    router.push(href)
    onClose()
  }

  return (
    <>
      {open && <div className="fixed inset-0 bg-black/50 z-40" onClick={onClose} />}

      <div
        className={`fixed left-0 top-0 h-full w-80 max-w-[80vw] glass-effect transform transition-transform duration-300 z-50 ${
          open ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        <div className="p-4">
          <div className="flex items-center justify-between mb-6">
            <Logo size="lg" />
            <Button variant="ghost" size="sm" onClick={onClose} className="text-white hover:bg-white/10">
              <X className="h-5 w-5" />
            </Button>
          </div>

          <nav className="space-y-2">
            {menuItems.map((item) => (
              <Button
                key={item.href}
                variant="ghost"
                className="w-full justify-start text-white hover:bg-white/10"
                onClick={() => handleNavigation(item.href)}
              >
                <item.icon className="h-5 w-5 mr-3" />
                {item.label}
              </Button>
            ))}
          </nav>

          <div className="absolute bottom-4 left-4 right-4">
            <Button variant="ghost" className="w-full justify-start text-red-400 hover:bg-red-500/10">
              <LogOut className="h-5 w-5 mr-3" />
              Sair
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}
