"use client"

import Image from "next/image"
import { useState } from "react"

interface TeamLogoProps {
  team: string
  logo?: string
  size?: "sm" | "md" | "lg"
  className?: string
}

export function TeamLogo({ team, logo, size = "md", className = "" }: TeamLogoProps) {
  const [imageError, setImageError] = useState(false)

  const sizes = {
    sm: { width: 20, height: 20, className: "w-5 h-5" },
    md: { width: 24, height: 24, className: "w-6 h-6" },
    lg: { width: 32, height: 32, className: "w-8 h-8" },
  }

  const currentSize = sizes[size]

  // Fallback para escudos genéricos baseados no nome do time
  const getTeamEmoji = (teamName: string) => {
    const teamEmojis: Record<string, string> = {
      Flamengo: "🔴⚫",
      Palmeiras: "🟢⚪",
      Corinthians: "⚪⚫",
      "São Paulo": "🔴⚪⚫",
      Santos: "⚪⚫",
      Botafogo: "⚫⚪",
      Fluminense: "🟢🔴⚪",
      Vasco: "⚫⚪",
      Grêmio: "🔵⚪⚫",
      Internacional: "🔴⚪",
      "Atlético-MG": "⚫⚪",
      Cruzeiro: "🔵⚪",
      Bahia: "🔵⚪🔴",
      Fortaleza: "🔵🔴",
      "Athletico-PR": "🔴⚫",
      "RB Bragantino": "🔴⚪",
      Ceará: "⚫⚪",
      Sport: "🔴⚫🟡",
      Chapecoense: "🟢⚪",
      Cuiabá: "🟢🟡",
    }

    return teamEmojis[teamName] || "⚽"
  }

  if (!logo || imageError) {
    return (
      <div className={`bg-white rounded-full flex items-center justify-center ${currentSize.className} ${className}`}>
        <span className="text-xs">{getTeamEmoji(team)}</span>
      </div>
    )
  }

  return (
    <div
      className={`bg-white rounded-full flex items-center justify-center overflow-hidden ${currentSize.className} ${className}`}
    >
      <Image
        src={logo || "/placeholder.svg"}
        alt={`${team} logo`}
        width={currentSize.width}
        height={currentSize.height}
        className="object-contain"
        onError={() => setImageError(true)}
        onLoad={() => setImageError(false)}
      />
    </div>
  )
}
