"use client"

import { useState, useEffect } from "react"
import { Head<PERSON> } from "@/components/header"
import { Sidebar } from "@/components/sidebar"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/hooks/use-auth"
import { Eye, RefreshCw, Printer } from "lucide-react"
import { BetTicketModal } from "@/components/bet-ticket-modal"
import Image from "next/image"
import { useRouter } from "next/navigation"

// Importar dinamicamente para evitar erros de SSR
import dynamic from "next/dynamic"

const BilhetePOS = dynamic(() => import("@/components/BilhetePOS"), {
  ssr: false,
  loading: () => <div className="p-4 text-center">Carregando bilhete...</div>
})

// Função para impressão térmica (será carregada dinamicamente)
const imprimirBilheteThermal = async (bilheteData: any) => {
  try {
    // Verificar se está rodando no <PERSON>mi
    if (typeof window !== 'undefined' && (window as any).sunmi) {
      const sunmi = (window as any).sunmi

      // Configurar impressora térmica
      await sunmi.printer.init()

      // Cabeçalho
      await sunmi.printer.setAlignment(1) // Centro
      await sunmi.printer.setFontSize(24)
      await sunmi.printer.printText('BOLÃO BRASIL\n')
      await sunmi.printer.printText('Sistema de Apostas Esportivas\n')
      await sunmi.printer.printText('--------------------------------\n')

      // Informações do bilhete
      await sunmi.printer.setAlignment(0) // Esquerda
      await sunmi.printer.setFontSize(18)
      await sunmi.printer.printText(`Código: ${bilheteData.codigo}\n`)
      await sunmi.printer.printText(`Data: ${bilheteData.data}\n`)
      await sunmi.printer.printText('--------------------------------\n')

      // Apostas
      await sunmi.printer.printText('SELEÇÕES:\n\n')

      for (const aposta of bilheteData.apostas) {
        await sunmi.printer.setFontSize(16)
        await sunmi.printer.printText(`${aposta.time1} x ${aposta.time2}\n`)
        await sunmi.printer.setFontSize(14)
        await sunmi.printer.printText(`${aposta.data} - ${aposta.horario}\n`)
        await sunmi.printer.printText(`${aposta.local}\n`)
        await sunmi.printer.printText(`${aposta.tipoAposta} - Odd: ${aposta.odd}\n`)
        await sunmi.printer.printText(`Valor: R$ ${aposta.valor.toFixed(2)}\n`)
        await sunmi.printer.printText('--------------------------------\n')
      }

      // Totais
      await sunmi.printer.setFontSize(18)
      await sunmi.printer.printText(`VALOR TOTAL: R$ ${bilheteData.valorTotal.toFixed(2)}\n`)
      await sunmi.printer.printText(`RETORNO POSSÍVEL: R$ ${bilheteData.possibleReturn.toFixed(2)}\n`)
      await sunmi.printer.printText('--------------------------------\n')

      // Rodapé
      await sunmi.printer.setAlignment(1) // Centro
      await sunmi.printer.setFontSize(12)
      await sunmi.printer.printText('Aposte com responsabilidade\n')
      await sunmi.printer.printText('pos.mmapay.pro\n')
      await sunmi.printer.printText('--------------------------------\n\n\n')

      // Finalizar impressão
      await sunmi.printer.lineWrap(3)

      return true

    } else {
      // Fallback para impressão web
      return false
    }

  } catch (error) {
    console.error('Erro ao imprimir:', error)
    return false
  }
}

export default function BilhetesPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [userBets, setUserBets] = useState<any[]>([])
  const [selectedBet, setSelectedBet] = useState<any>(null)
  const [showTicketModal, setShowTicketModal] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isPrinting, setIsPrinting] = useState(false)
  const { user } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // Pequeno delay para garantir que a página esteja totalmente carregada
    const timer = setTimeout(() => {
      loadBets()
    }, 300)

    return () => clearTimeout(timer)
  }, [])

  const loadBets = async () => {
    setIsLoading(true)
    try {
      // Small delay to ensure localStorage is ready
      await new Promise((resolve) => setTimeout(resolve, 100))

      const storedBets = localStorage.getItem("userBets")
      console.log("Raw localStorage data:", storedBets)

      if (storedBets) {
        const parsedBets = JSON.parse(storedBets)
        console.log("Bilhetes carregados:", parsedBets)

        if (Array.isArray(parsedBets) && parsedBets.length > 0) {
          setUserBets(parsedBets)
        } else {
          console.log("Nenhum bilhete encontrado ou formato inválido")
          setUserBets([])
        }
      } else {
        console.log("Nenhum dado de bilhetes no localStorage")
        setUserBets([])
      }
    } catch (error) {
      console.error("Erro ao carregar bilhetes:", error)
      setUserBets([])
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "PENDENTE":
        return "bg-yellow-500"
      case "GANHOU":
        return "bg-green-500"
      case "PERDEU":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const handleViewDetails = (bet: any) => {
    setSelectedBet(bet)
    setShowTicketModal(true)
  }

  // Função para impressão térmica Sunmi
  const handlePrintThermal = async (bet: any) => {
    setIsPrinting(true)

    try {
      // Converter dados do bilhete para formato compatível
      const bilheteData = {
        codigo: bet.id,
        data: new Date(bet.timestamp || Date.now()).toLocaleString('pt-BR'),
        apostas: bet.bets.map((betItem: any) => ({
          id: betItem.id || Math.random().toString(),
          time1: betItem.match?.homeTeam || betItem.homeTeam || 'Time 1',
          time2: betItem.match?.awayTeam || betItem.awayTeam || 'Time 2',
          logo1: `/logos/${(betItem.match?.homeTeam || betItem.homeTeam || 'default').toLowerCase().replace(/\s+/g, '-')}.png`,
          logo2: `/logos/${(betItem.match?.awayTeam || betItem.awayTeam || 'default').toLowerCase().replace(/\s+/g, '-')}.png`,
          data: new Date(betItem.match?.date || betItem.date || Date.now()).toLocaleDateString('pt-BR'),
          horario: new Date(betItem.match?.date || betItem.date || Date.now()).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }),
          local: betItem.match?.venue || betItem.venue || 'Estádio',
          tipoAposta: betItem.team || betItem.betType || 'Vitória',
          odd: betItem.odds || 1.0,
          valor: bet.amount / bet.bets.length // Dividir valor total pelo número de apostas
        })),
        valorTotal: bet.amount,
        possibleReturn: bet.potentialReturn || bet.amount * 2
      }

      const sucesso = await imprimirBilheteThermal(bilheteData)

      if (sucesso) {
        alert('✅ Bilhete impresso na impressora térmica Sunmi!')
      } else {
        alert('⚠️ Impressora térmica não disponível. Usando impressão web...')
        handleDownloadPDF(bet)
      }
    } catch (error) {
      console.error('Erro ao imprimir:', error)
      alert('❌ Erro ao imprimir. Tentando impressão alternativa...')
      handleDownloadPDF(bet)
    } finally {
      setIsPrinting(false)
    }
  }

  const handleDownloadPDF = (bet: any) => {
    setIsPrinting(true)

    try {
      const printWindow = window.open("", "_blank")
      if (!printWindow) {
        alert("Por favor, permita pop-ups para imprimir o bilhete.")
        setIsPrinting(false)
        return
      }

      printWindow.document.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Bilhete ${bet.id}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          @media print {
            @page {
              size: 80mm 297mm;
              margin: 0;
            }
            body {
              margin: 5mm;
            }
          }
          body { 
            font-family: 'Courier New', monospace; 
            padding: 15px; 
            background: white;
            color: black;
            max-width: 350px;
            margin: 0 auto;
            font-size: 12px;
            line-height: 1.3;
          }
          .ticket { 
            border: 2px solid #000;
            padding: 12px;
          }
          .header { 
            text-align: center; 
            margin-bottom: 15px; 
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
          }
          .logo {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .logo-icon {
            margin-right: 5px;
            font-size: 18px;
          }
          .team-logos {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 5px 0;
          }
          .team-logo-print {
            width: 20px;
            height: 20px;
            margin: 0 5px;
            border-radius: 50%;
            border: 1px solid #000;
          }
          .subtitle {
            font-size: 11px;
            margin: 2px 0;
          }
          .info-section {
            margin: 10px 0;
          }
          .info-line {
            margin: 3px 0;
            display: flex;
            justify-content: space-between;
            font-size: 11px;
          }
          .info-line strong {
            font-weight: bold;
          }
          .section-title {
            font-weight: bold;
            margin: 15px 0 8px 0;
            text-align: center;
            border-top: 1px dotted #000;
            border-bottom: 1px dotted #000;
            padding: 5px 0;
            font-size: 12px;
          }
          .bet-item { 
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px dotted #ccc;
          }
          .match-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 3px;
            font-size: 11px;
          }
          .team-vs {
            display: flex;
            align-items: center;
            flex: 1;
          }
          .team-name {
            display: inline-flex;
            align-items: center;
            font-size: 10px;
          }
          .team-logo {
            width: 16px;
            height: 16px;
            margin-right: 3px;
            border-radius: 50%;
            background: #f0f0f0;
            border: 1px solid #ddd;
          }
          .vs-text {
            margin: 0 5px;
            font-size: 10px;
            color: #666;
          }
          .match-date {
            font-size: 9px;
            color: #666;
            text-align: right;
          }
          .bet-selection {
            font-weight: bold;
            font-size: 11px;
            margin-top: 2px;
            color: #000;
          }
          .totals-section { 
            margin-top: 15px;
            border-top: 1px solid #000;
            padding-top: 8px;
          }
          .total-line {
            margin: 3px 0;
            display: flex;
            justify-content: space-between;
            font-size: 11px;
          }
          .total-line.final {
            font-weight: bold;
            border-top: 1px dotted #000;
            padding-top: 5px;
            margin-top: 5px;
          }
          .qr-section {
            text-align: center;
            margin: 15px 0;
            border-top: 1px dotted #000;
            padding-top: 10px;
          }
          .qr-placeholder {
            width: 80px;
            height: 80px;
            border: 2px solid #000;
            margin: 10px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            background: #f9f9f9;
          }
          .footer { 
            text-align: center; 
            margin-top: 15px; 
            font-size: 9px;
            border-top: 1px solid #000;
            padding-top: 8px;
            line-height: 1.4;
          }
          .footer-section {
            margin: 5px 0;
          }
          .company-info {
            border-top: 1px dotted #000;
            padding-top: 8px;
            margin-top: 8px;
          }
          .status-box {
            border: 1px solid #000;
            padding: 3px 8px;
            margin: 8px 0;
            text-align: center;
            font-weight: bold;
            font-size: 11px;
          }
          .print-instructions {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            display: block;
          }
          @media print {
            .print-instructions {
              display: none;
            }
          }
          .print-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            font-size: 16px;
            cursor: pointer;
            border-radius: 5px;
            margin: 10px 0;
            display: block;
            width: 100%;
          }
        </style>
      </head>
      <body>
        <div class="print-instructions">
          <h3>Instruções de Impressão</h3>
          <p>1. Clique no botão "Imprimir Agora" abaixo</p>
          <p>2. Selecione sua impressora térmica ou normal</p>
          <p>3. Para impressoras térmicas, escolha o tamanho 80mm</p>
          <p>4. Para impressoras normais, escolha "Sem margens" se disponível</p>
          <button class="print-button" onclick="window.print(); return false;">Imprimir Agora</button>
        </div>
        
        <div class="ticket">
          <div class="header">
            <div class="logo">
              <span class="logo-icon">🏆</span>
              <span>BOLÃO BRASIL</span>
            </div>
            <div class="subtitle">Sistema de Apostas Esportivas</div>
            <div class="subtitle">www.bolaobrasil.com</div>
          </div>
          
          <div class="info-section">
            <div class="info-line">
              <span>BILHETE:</span>
              <span><strong>${bet.id}</strong></span>
            </div>
            <div class="info-line">
              <span>Data:</span>
              <span>${bet.date?.split(",")[0] || ""}</span>
            </div>
            <div class="info-line">
              <span>Hora:</span>
              <span>${bet.date?.split(",")[1]?.trim() || ""}</span>
            </div>
            <div class="info-line">
              <span>Cliente:</span>
              <span>${user?.name || "Admin"}</span>
            </div>
          </div>

          <div class="status-box">
            STATUS: ${bet.status}
          </div>
          
          <div class="section-title">APOSTAS SELECIONADAS</div>
          
          ${bet.bets
            .map(
              (betItem: any) => `
              <div class="bet-item">
                <div class="match-info">
                  <div class="team-vs">
                    <span class="team-name">
                      <img src="${betItem.match.homeTeamLogo}" class="team-logo" alt="${betItem.match.homeTeam}">
                      ${betItem.match.homeTeam}
                    </span>
                    <span class="vs-text">x</span>
                    <span class="team-name">
                      <img src="${betItem.match.awayTeamLogo}" class="team-logo" alt="${betItem.match.awayTeam}">
                      ${betItem.match.awayTeam}
                    </span>
                  </div>
                  <div class="match-date">${betItem.match.date?.split(" - ")[0] || ""}</div>
                </div>
                <div class="bet-selection">${betItem.team}</div>
              </div>
            `,
            )
            .join("")}
          
          <div class="totals-section">
            <div class="total-line">
              <span>Qtd. Apostas:</span>
              <span>${bet.bets.length}</span>
            </div>
            <div class="total-line final">
              <span>Valor Apostado:</span>
              <span><strong>R$ ${bet.amount.toFixed(2)}</strong></span>
            </div>
          </div>
          
          <div class="qr-section">
            <div class="qr-placeholder">
              QR CODE
            </div>
          </div>
          
          <div class="footer">
            <div class="footer-section">
              <div>Guarde este bilhete para conferir o resultado</div>
              <div>Válido por 30 dias após o último jogo</div>
            </div>
            
            <div class="footer-section">
              <div><strong>BOA SORTE!</strong></div>
              <div>Jogue com responsabilidade</div>
              <div>+18 anos</div>
            </div>
            
            <div class="company-info">
              <div><strong>BOLÃO BRASIL LTDA</strong></div>
              <div>CNPJ: 00.000.000/0001-00</div>
              <div>Tel: (11) 9999-9999</div>
            </div>
          </div>
        </div>
        
        <div class="print-instructions">
          <p>Se a impressão não ficar boa, tente ajustar as configurações de impressão:</p>
          <ul>
            <li>Desative "Cabeçalhos e rodapés"</li>
            <li>Escolha "Ajustar à página" ou "Tamanho real"</li>
            <li>Para impressoras térmicas, escolha papel 80mm</li>
          </ul>
          <button class="print-button" onclick="window.print(); return false;">Imprimir Agora</button>
        </div>
        
        <script>
          // Auto-print after 1 second
          setTimeout(function() {
            window.print();
          }, 1000);
        </script>
      </body>
      </html>
    `)
      printWindow.document.close()
    } catch (error) {
      console.error("Erro ao imprimir:", error)
      alert("Ocorreu um erro ao tentar imprimir. Por favor, tente novamente.")
    } finally {
      setIsPrinting(false)
    }
  }

  const handleNewBet = () => {
    router.push("/")
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-blue-800">
        <Header onMenuClick={() => setSidebarOpen(true)} onBetSlipClick={() => {}} />
        <main className="pt-20 pb-6 px-4">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center justify-center py-20">
              <div className="text-center">
                <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-white" />
                <p className="text-white">Carregando bilhetes...</p>
              </div>
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-blue-800">
      <Header onMenuClick={() => setSidebarOpen(true)} onBetSlipClick={() => {}} />

      <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} user={user} />

      <main className="pt-20 pb-6 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-white">Meus Bilhetes</h1>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={loadBets} className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4" />
                Atualizar
              </Button>
              <Button variant="default" size="sm" onClick={handleNewBet} className="flex items-center gap-2">
                Nova Aposta
              </Button>
            </div>
          </div>

          {userBets.length === 0 ? (
            <Card className="glass-effect border-white/20 p-8 text-center">
              <p className="text-gray-400">Nenhum bilhete encontrado</p>
              <p className="text-sm text-gray-500 mt-2">Faça sua primeira aposta para ver seus bilhetes aqui</p>
              <Button variant="default" size="sm" onClick={handleNewBet} className="mt-4">
                Fazer uma Aposta
              </Button>
            </Card>
          ) : (
            <div className="space-y-4">
              {userBets.map((bet) => (
                <Card key={bet.id} className="glass-effect border-white/20 p-4">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="font-mono text-sm text-white">#{bet.id}</span>
                        <Badge className={`${getStatusColor(bet.status)} text-white`}>{bet.status}</Badge>
                      </div>
                      <div className="text-sm text-gray-400">{bet.date}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-400">Valor Apostado</div>
                      <div className="font-semibold text-white">R$ {bet.amount.toFixed(2)}</div>
                    </div>
                  </div>

                  <div className="space-y-2 mb-4">
                    {bet.bets.map((betItem: any, index: number) => (
                      <div key={index} className="flex justify-between items-center text-sm p-3 glass-effect rounded">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-2">
                            <div className="w-5 h-5 bg-white rounded-full flex items-center justify-center overflow-hidden">
                              <Image
                                src={betItem.match.homeTeamLogo || "/placeholder.svg"}
                                alt={betItem.match.homeTeam}
                                width={20}
                                height={20}
                                className="object-contain"
                              />
                            </div>
                            <span className="text-xs text-gray-400">vs</span>
                            <div className="w-5 h-5 bg-white rounded-full flex items-center justify-center overflow-hidden">
                              <Image
                                src={betItem.match.awayTeamLogo || "/placeholder.svg"}
                                alt={betItem.match.awayTeam}
                                width={20}
                                height={20}
                                className="object-contain"
                              />
                            </div>
                          </div>
                          <div>
                            <div className="font-medium text-white">
                              {betItem.match.homeTeam} x {betItem.match.awayTeam}
                            </div>
                            <div className="text-gray-400">
                              <span className="text-green-400 font-medium">{betItem.team}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="flex items-center justify-between pt-4 border-t border-white/20">
                    <div className="text-sm text-white">
                      <span className="text-gray-400">Qtd. Apostas: </span>
                      <span>{bet.bets.length}</span>
                    </div>
                  </div>

                  <div className="flex space-x-2 mt-4">
                    <Button variant="outline" size="sm" className="flex-1" onClick={() => handleViewDetails(bet)}>
                      <Eye className="h-4 w-4 mr-2" />
                      Ver Detalhes
                    </Button>
                    <Button
                      variant="default"
                      size="sm"
                      className="flex-1 bg-green-600 hover:bg-green-700"
                      onClick={() => handlePrintThermal(bet)}
                      disabled={isPrinting}
                    >
                      <Printer className="h-4 w-4 mr-2" />
                      {isPrinting ? "Imprimindo..." : "🖨️ Sunmi"}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => handleDownloadPDF(bet)}
                      disabled={isPrinting}
                    >
                      <Printer className="h-4 w-4 mr-2" />
                      {isPrinting ? "Imprimindo..." : "📄 Web"}
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      </main>

      {showTicketModal && selectedBet && (
        <BetTicketModal bet={selectedBet} user={user} onClose={() => setShowTicketModal(false)} />
      )}
    </div>
  )
}
