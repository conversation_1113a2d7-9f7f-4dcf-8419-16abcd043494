"use client"

import { useState } from "react"
import { Header } from "@/components/header"
import { Sidebar } from "@/components/sidebar"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useAuth } from "@/hooks/use-auth"
import { useBetSlip } from "@/hooks/use-bet-slip"
import { useRouter } from "next/navigation"
import { Loader2, ArrowLeft, ShoppingCart } from "lucide-react"
import Image from "next/image"

export default function BetSlipPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { user } = useAuth()
  const { bets, clearBets, setTotalValue } = useBetSlip()
  const [betAmount, setBetAmount] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  const router = useRouter()

  const isValidBetAmount = betAmount && !isNaN(Number.parseFloat(betAmount)) && Number.parseFloat(betAmount) > 0

  const handlePlaceBet = async () => {
    if (bets.length === 0 || !betAmount) return

    setIsProcessing(true)

    try {
      // Simulate bet placement
      const betData = {
        id: `BT${Date.now()}`,
        bets: bets.map((bet) => ({
          ...bet,
          match: {
            ...bet.match,
            date: bet.match.date || new Date().toLocaleDateString("pt-BR"),
          },
        })),
        amount: Number.parseFloat(betAmount),
        status: "PENDENTE",
        date: new Date().toLocaleString("pt-BR"),
      }

      // Store bet in localStorage for demo
      const existingBets = JSON.parse(localStorage.getItem("userBets") || "[]")
      existingBets.push(betData)
      localStorage.setItem("userBets", JSON.stringify(existingBets))

      // Store the bet amount for the ticket
      setTotalValue(Number.parseFloat(betAmount))

      clearBets()
      setBetAmount("")

      // Small delay to ensure state is updated
      await new Promise((resolve) => setTimeout(resolve, 500))

      // Navigate to tickets page
      router.push("/bilhetes")
    } catch (error) {
      console.error("Erro ao processar aposta:", error)
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-blue-800">
      <Header onMenuClick={() => setSidebarOpen(true)} onBetSlipClick={() => {}} />

      <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} user={user} />

      <main className="pt-20 pb-6 px-4">
        <div className="max-w-md mx-auto">
          <div className="flex items-center justify-between mb-6">
            <Button variant="ghost" onClick={() => router.back()} className="text-gray-400">
              <ArrowLeft className="h-5 w-5 mr-2" />
              Voltar
            </Button>
            <h1 className="text-2xl font-bold text-white">Finalizar Aposta</h1>
          </div>

          <Card className="glass-effect border-white/20 p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4 text-white">Suas Seleções</h2>

            {bets.length === 0 ? (
              <div className="text-center text-gray-400 py-8">
                <p>Nenhuma aposta selecionada</p>
                <Button variant="outline" className="mt-4" onClick={() => router.push("/")}>
                  Selecionar Jogos
                </Button>
              </div>
            ) : (
              <div className="space-y-3">
                {bets.map((bet) => (
                  <Card key={bet.id} className="glass-effect border-white/20 p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center overflow-hidden">
                            <Image
                              src={bet.match.homeTeamLogo || "/placeholder.svg"}
                              alt={bet.match.homeTeam}
                              width={24}
                              height={24}
                              className="object-contain"
                            />
                          </div>
                          <span className="text-xs text-gray-400">vs</span>
                          <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center overflow-hidden">
                            <Image
                              src={bet.match.awayTeamLogo || "/placeholder.svg"}
                              alt={bet.match.awayTeam}
                              width={24}
                              height={24}
                              className="object-contain"
                            />
                          </div>
                        </div>
                        <div>
                          <div className="font-medium text-sm text-white">
                            {bet.match.homeTeam} x {bet.match.awayTeam}
                          </div>
                          <div className="text-green-400 font-medium text-sm">{bet.team}</div>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </Card>

          {bets.length > 0 && (
            <Card className="glass-effect border-white/20 p-6">
              <div className="space-y-4">
                <div className="flex justify-between text-sm text-white">
                  <span>Quantidade de Apostas:</span>
                  <span>{bets.length}</span>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2 text-white">Valor da Aposta (R$)</label>
                  <Input
                    type="number"
                    value={betAmount}
                    onChange={(e) => setBetAmount(e.target.value)}
                    placeholder="0,00"
                    className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                  />
                </div>

                <Button
                  onClick={handlePlaceBet}
                  disabled={!isValidBetAmount || bets.length === 0 || isProcessing}
                  className="w-full bg-green-600 hover:bg-green-700 h-12 text-base"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                      Processando...
                    </>
                  ) : (
                    <>
                      <ShoppingCart className="h-5 w-5 mr-2" />
                      Finalizar Aposta
                    </>
                  )}
                </Button>
              </div>
            </Card>
          )}
        </div>
      </main>
    </div>
  )
}
