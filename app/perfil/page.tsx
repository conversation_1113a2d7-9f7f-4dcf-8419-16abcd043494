"use client"

import { useState } from "react"
import { Header } from "@/components/header"
import { Sidebar } from "@/components/sidebar"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useAuth } from "@/hooks/use-auth"
import { User, Mail, Phone, MapPin, Edit } from "lucide-react"

export default function PerfilPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [editing, setEditing] = useState(false)
  const { user } = useAuth()

  return (
    <div className="min-h-screen">
      <Header onMenuClick={() => setSidebarOpen(true)} onBetSlipClick={() => {}} />

      <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} user={user} />

      <main className="pt-20 pb-6 px-4">
        <div className="max-w-2xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold">Meu Perfil</h1>
            <Button onClick={() => setEditing(!editing)} variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              {editing ? "Cancelar" : "Editar"}
            </Button>
          </div>

          <Card className="glass-effect border-white/20 p-6 mb-6">
            <div className="flex items-center space-x-4 mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center">
                <User className="h-10 w-10 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">{user?.name}</h2>
                <p className="text-gray-400">{user?.email}</p>
                {user?.isAdmin && (
                  <span className="inline-block bg-yellow-500 text-black text-xs px-2 py-1 rounded-full mt-1">
                    Administrador
                  </span>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-gray-400" />
                <div className="flex-1">
                  <label className="block text-sm text-gray-400 mb-1">Email</label>
                  {editing ? (
                    <Input defaultValue={user?.email} className="bg-white/10 border-white/20 text-white" />
                  ) : (
                    <p>{user?.email}</p>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-gray-400" />
                <div className="flex-1">
                  <label className="block text-sm text-gray-400 mb-1">Telefone</label>
                  {editing ? (
                    <Input placeholder="(11) 99999-9999" className="bg-white/10 border-white/20 text-white" />
                  ) : (
                    <p className="text-gray-400">Não informado</p>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <MapPin className="h-5 w-5 text-gray-400" />
                <div className="flex-1">
                  <label className="block text-sm text-gray-400 mb-1">Endereço</label>
                  {editing ? (
                    <Input placeholder="Seu endereço" className="bg-white/10 border-white/20 text-white" />
                  ) : (
                    <p className="text-gray-400">Não informado</p>
                  )}
                </div>
              </div>
            </div>

            {editing && (
              <div className="flex space-x-3 mt-6">
                <Button className="flex-1 bg-green-600 hover:bg-green-700">Salvar Alterações</Button>
                <Button variant="outline" className="flex-1" onClick={() => setEditing(false)}>
                  Cancelar
                </Button>
              </div>
            )}
          </Card>

          <Card className="glass-effect border-white/20 p-6">
            <h3 className="text-lg font-semibold mb-4">Informações da Conta</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-gray-400">Saldo Atual</div>
                <div className="text-2xl font-bold text-green-400">R$ {user?.balance.toFixed(2)}</div>
              </div>
              <div>
                <div className="text-sm text-gray-400">Apostas Realizadas</div>
                <div className="text-2xl font-bold">12</div>
              </div>
            </div>

            <div className="mt-6 space-y-2">
              <Button className="w-full bg-green-600 hover:bg-green-700">Fazer Depósito</Button>
              <Button variant="outline" className="w-full">
                Solicitar Saque
              </Button>
            </div>
          </Card>
        </div>
      </main>
    </div>
  )
}
