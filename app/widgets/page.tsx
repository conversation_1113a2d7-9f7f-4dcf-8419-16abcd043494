"use client"

import { useState } from "react"
import { Header } from "@/components/header"
import { Sidebar } from "@/components/sidebar"
import { Card } from "@/components/ui/card"
import { useAuth } from "@/hooks/use-auth"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function WidgetsPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { user } = useAuth()

  // CLIENT_ID de exemplo - em produção, isso viria de variáveis de ambiente
  const CLIENT_ID = "demo_client_id_12345"

  return (
    <div className="min-h-screen">
      <Header onMenuClick={() => setSidebarOpen(true)} onBetSlipClick={() => {}} />

      <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} user={user} />

      <main className="pt-20 pb-6 px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Widgets da API Futebol</h1>
          <p className="text-gray-400 mb-6">Widgets interativos com dados em tempo real dos campeonatos brasileiros</p>

          <Tabs defaultValue="rodadas" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="rodadas">Widget de Rodadas</TabsTrigger>
              <TabsTrigger value="tabela">Widget de Tabela</TabsTrigger>
            </TabsList>

            <TabsContent value="rodadas">
              <Card className="glass-effect border-white/20 p-6">
                <h2 className="text-xl font-semibold mb-4">Widget de Rodadas</h2>
                <p className="text-gray-400 mb-4">
                  Visão abrangente das rodadas em torneios ou campeonatos, com resultados de jogos, datas e outras
                  informações relevantes.
                </p>

                <div className="bg-white/5 rounded-lg p-4">
                  <iframe
                    src={`https://api.api-futebol.com.br/v1/widgets/rodadas?client_id=${CLIENT_ID}`}
                    title="API Futebol - Widget de Rodadas"
                    width="100%"
                    frameBorder="0"
                    style={{
                      height: "100%",
                      minHeight: "700px",
                      maxWidth: "100%",
                      borderRadius: "8px",
                    }}
                    className="rounded-lg"
                  />
                </div>

                <div className="mt-4 p-4 bg-gray-800/50 rounded-lg">
                  <h3 className="text-sm font-semibold mb-2">Código de Integração:</h3>
                  <pre className="text-xs text-gray-300 overflow-x-auto">
                    {`<!-- API Futebol Widget de Rodadas -->
<iframe
    src="https://api.api-futebol.com.br/v1/widgets/rodadas?client_id=[CLIENT_ID]"
    title="API Futebol"
    width="100%"
    frameborder="0"
    style="height: 100%; min-height: 700px; max-width: 300px;">
</iframe>`}
                  </pre>
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="tabela">
              <Card className="glass-effect border-white/20 p-6">
                <h2 className="text-xl font-semibold mb-4">Widget de Tabela</h2>
                <p className="text-gray-400 mb-4">
                  Classificação atual de times em um torneio ou campeonato de forma clara e visual, com informações
                  atualizadas em tempo real.
                </p>

                <div className="bg-white/5 rounded-lg p-4">
                  {/* Widget de Tabela */}
                  <div
                    className="apifutebol-tabela min-h-[500px] flex items-center justify-center text-gray-400"
                    data-client-id={CLIENT_ID}
                  >
                    <div className="text-center">
                      <div className="text-lg mb-2">Widget de Tabela</div>
                      <div className="text-sm">
                        O widget será carregado aqui quando o script da API Futebol for incluído
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-4 p-4 bg-gray-800/50 rounded-lg">
                  <h3 className="text-sm font-semibold mb-2">Código de Integração:</h3>
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-xs font-semibold mb-1">1. Adicione no &lt;head&gt;:</h4>
                      <pre className="text-xs text-gray-300 overflow-x-auto">
                        {`<!-- API Futebol Widget Script -->
<script src="https://cdn.api-futebol.com.br/widgets/v1/apifutebol-tabela.js"></script>`}
                      </pre>
                    </div>
                    <div>
                      <h4 className="text-xs font-semibold mb-1">2. Adicione onde quiser exibir:</h4>
                      <pre className="text-xs text-gray-300 overflow-x-auto">
                        {`<!-- Exemplo para o Widget de Tabela -->
<div class="apifutebol-tabela" data-client-id="CLIENT_ID"></div>`}
                      </pre>
                    </div>
                  </div>
                </div>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Informações Adicionais */}
          <Card className="glass-effect border-white/20 p-6 mt-6">
            <h2 className="text-xl font-semibold mb-4">Sobre os Widgets</h2>
            <div className="space-y-4 text-gray-300">
              <p>
                Os widgets da API Futebol são componentes interativos e autônomos que podem ser facilmente integrados a
                sites, aplicativos ou plataformas digitais.
              </p>
              <p>
                Eles proporcionam uma maneira simples de adicionar funcionalidades específicas, como mostrar informações
                atualizadas, sem a necessidade de desenvolver ou programar do zero.
              </p>
              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                <h3 className="text-yellow-400 font-semibold mb-2">⚠️ Nota Importante</h3>
                <p className="text-sm">
                  Para utilizar os widgets em produção, você precisa de um CLIENT_ID válido fornecido pela API Futebol.
                  Substitua [CLIENT_ID] pelo valor correspondente fornecido em seu dashboard.
                </p>
              </div>
            </div>
          </Card>
        </div>
      </main>
    </div>
  )
}
