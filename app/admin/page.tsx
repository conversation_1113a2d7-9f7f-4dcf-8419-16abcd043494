"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/header"
import { Sidebar } from "@/components/sidebar"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { BannerSettings } from "@/components/admin/banner-settings"
import { useAuth } from "@/hooks/use-auth"
import { Users, DollarSign, Receipt, TrendingUp } from "lucide-react"

export default function AdminPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { user } = useAuth()

  const stats = [
    { title: "Usuários Ativos", value: "1,234", icon: Users, color: "text-blue-400" },
    { title: "Volume de Apostas", value: "R$ 45.678", icon: DollarSign, color: "text-green-400" },
    { title: "Bilhetes Hoje", value: "89", icon: Receipt, color: "text-yellow-400" },
    { title: "Lucro Mensal", value: "R$ 12.345", icon: TrendingUp, color: "text-purple-400" },
  ]

  const recentBets = [
    { id: "BT001", user: "João Silva", amount: 50.0, status: "PENDENTE" },
    { id: "BT002", user: "Maria Santos", amount: 25.0, status: "GANHOU" },
    { id: "BT003", user: "Pedro Costa", amount: 100.0, status: "PERDEU" },
  ]

  if (!user?.isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="glass-effect border-white/20 p-8 text-center">
          <h1 className="text-2xl font-bold text-red-400 mb-4">Acesso Negado</h1>
          <p className="text-gray-400">Você não tem permissão para acessar esta página.</p>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <Header onMenuClick={() => setSidebarOpen(true)} onBetSlipClick={() => {}} />

      <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} user={user} />

      <main className="pt-20 pb-6 px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Painel Administrativo</h1>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            {stats.map((stat) => (
              <Card key={stat.title} className="glass-effect border-white/20 p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">{stat.title}</p>
                    <p className={`text-2xl font-bold ${stat.color}`}>{stat.value}</p>
                  </div>
                  <stat.icon className={`h-8 w-8 ${stat.color}`} />
                </div>
              </Card>
            ))}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Banner Settings */}
            <Card className="glass-effect border-white/20 p-0 overflow-hidden">
              <BannerSettings />
            </Card>

            {/* Recent Bets */}
            <Card className="glass-effect border-white/20 p-6">
              <h2 className="text-xl font-semibold mb-4">Apostas Recentes</h2>
              <div className="space-y-3">
                {recentBets.map((bet) => (
                  <div key={bet.id} className="flex items-center justify-between p-3 glass-effect rounded-lg">
                    <div>
                      <div className="font-medium">{bet.user}</div>
                      <div className="text-sm text-gray-400">#{bet.id}</div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">R$ {bet.amount.toFixed(2)}</div>
                      <Badge
                        className={`text-xs ${
                          bet.status === "PENDENTE"
                            ? "bg-yellow-500"
                            : bet.status === "GANHOU"
                              ? "bg-green-500"
                              : "bg-red-500"
                        }`}
                      >
                        {bet.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Quick Actions */}
            <Card className="glass-effect border-white/20 p-6">
              <h2 className="text-xl font-semibold mb-4">Ações Rápidas</h2>
              <div className="space-y-3">
                <Button className="w-full justify-start" variant="outline">
                  Gerenciar Usuários
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  Configurar Odds
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  Relatórios Financeiros
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  Configurações do Sistema
                </Button>
              </div>
            </Card>
          </div>

          {/* User Management */}
          <Card className="glass-effect border-white/20 p-6 mt-6">
            <h2 className="text-xl font-semibold mb-4">Gerenciar Usuários</h2>
            <div className="flex space-x-4 mb-4">
              <Input placeholder="Buscar usuário..." className="bg-white/10 border-white/20 text-white" />
              <Button>Buscar</Button>
              <Button variant="outline">Novo Usuário</Button>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-white/20">
                    <th className="text-left p-2">Nome</th>
                    <th className="text-left p-2">Email</th>
                    <th className="text-left p-2">Saldo</th>
                    <th className="text-left p-2">Status</th>
                    <th className="text-left p-2">Ações</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-white/10">
                    <td className="p-2">João Silva</td>
                    <td className="p-2"><EMAIL></td>
                    <td className="p-2">R$ 150,00</td>
                    <td className="p-2">
                      <Badge className="bg-green-500">Ativo</Badge>
                    </td>
                    <td className="p-2">
                      <Button size="sm" variant="outline">
                        Editar
                      </Button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </Card>
        </div>
      </main>
    </div>
  )
}
