/* Estilos específicos para Sunmi V1s-G */
/* Resolução: 480x800, Android 6.0, 1GB RAM */

@media screen and (max-width: 480px) and (max-height: 800px) {
  /* Layout otimizado para Sunmi V1s-G */
  
  .sunmi-container {
    max-width: 480px;
    margin: 0 auto;
    padding: 8px;
    font-size: 14px;
  }
  
  .sunmi-header {
    height: 50px;
    background: #1e40af;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: bold;
  }
  
  .sunmi-button {
    min-height: 40px;
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 4px;
    border: none;
    background: #1e40af;
    color: white;
    cursor: pointer;
    margin: 4px;
    width: 100%;
    max-width: 200px;
  }
  
  .sunmi-button:hover {
    background: #1d4ed8;
  }
  
  .sunmi-button:active {
    background: #1e3a8a;
  }
  
  .sunmi-input {
    width: 100%;
    padding: 8px;
    font-size: 14px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    margin: 4px 0;
  }
  
  .sunmi-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 12px;
    margin: 8px 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .sunmi-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin: 8px 0;
  }
  
  .sunmi-text-small {
    font-size: 12px;
    color: #6b7280;
  }
  
  .sunmi-text-medium {
    font-size: 14px;
    color: #374151;
  }
  
  .sunmi-text-large {
    font-size: 16px;
    color: #111827;
    font-weight: 600;
  }
  
  .sunmi-navigation {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: white;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 1000;
  }
  
  .sunmi-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    color: #6b7280;
    text-decoration: none;
    font-size: 10px;
  }
  
  .sunmi-nav-item.active {
    color: #1e40af;
  }
  
  .sunmi-nav-icon {
    width: 20px;
    height: 20px;
    margin-bottom: 2px;
  }
  
  /* Otimizações de performance para dispositivos com pouca RAM */
  .sunmi-optimized {
    transform: translateZ(0); /* Força aceleração de hardware */
    will-change: auto;
  }
  
  /* Reduzir animações para melhor performance */
  .sunmi-no-animation {
    animation: none !important;
    transition: none !important;
  }
  
  /* Layout específico para apostas */
  .sunmi-bet-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 10px;
    margin: 6px 0;
  }
  
  .sunmi-bet-teams {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 0;
  }
  
  .sunmi-team {
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    flex: 1;
  }
  
  .sunmi-vs {
    font-size: 10px;
    color: #6b7280;
    margin: 0 8px;
  }
  
  .sunmi-odds {
    display: flex;
    justify-content: space-between;
    margin: 8px 0;
  }
  
  .sunmi-odd-button {
    flex: 1;
    margin: 0 2px;
    padding: 6px;
    font-size: 12px;
    background: #f1f5f9;
    border: 1px solid #cbd5e1;
    border-radius: 4px;
    text-align: center;
  }
  
  .sunmi-odd-button.selected {
    background: #1e40af;
    color: white;
    border-color: #1e40af;
  }
  
  /* Formulário de aposta */
  .sunmi-bet-form {
    background: white;
    border-radius: 6px;
    padding: 12px;
    margin: 8px 0;
    border: 1px solid #e5e7eb;
  }
  
  .sunmi-amount-input {
    width: 100%;
    padding: 8px;
    font-size: 16px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    text-align: center;
  }
  
  .sunmi-quick-amounts {
    display: flex;
    justify-content: space-between;
    margin: 8px 0;
  }
  
  .sunmi-quick-amount {
    flex: 1;
    margin: 0 2px;
    padding: 6px;
    font-size: 12px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    text-align: center;
  }
  
  /* Responsividade para orientação landscape */
  @media screen and (orientation: landscape) {
    .sunmi-container {
      padding: 4px;
    }
    
    .sunmi-grid {
      grid-template-columns: 1fr 1fr 1fr;
    }
    
    .sunmi-navigation {
      height: 50px;
    }
  }
}

/* Estilos para modo escuro (opcional) */
@media (prefers-color-scheme: dark) {
  .sunmi-container {
    background: #1f2937;
    color: white;
  }
  
  .sunmi-card {
    background: #374151;
    border-color: #4b5563;
  }
  
  .sunmi-input {
    background: #374151;
    border-color: #4b5563;
    color: white;
  }
}
