"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/header"
import { Sidebar } from "@/components/sidebar"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { useAuth } from "@/hooks/use-auth"
import { ArrowUpRight, ArrowDownLeft, Filter } from "lucide-react"

export default function TransacoesPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { user } = useAuth()

  const transactions = [
    {
      id: "TXN001",
      type: "DEPOSITO",
      amount: 100.0,
      date: "05/06/2025 14:30",
      status: "CONCLUIDO",
      description: "De<PERSON><PERSON><PERSON> via PIX",
    },
    {
      id: "TXN002",
      type: "APOSTA",
      amount: -25.0,
      date: "05/06/2025 15:45",
      status: "CONCLUIDO",
      description: "Aposta #BT289100516",
    },
    {
      id: "TXN003",
      type: "GANHO",
      amount: 75.5,
      date: "04/06/2025 20:15",
      status: "CONCLUIDO",
      description: "Ganho da aposta #BT289100515",
    },
    {
      id: "TXN004",
      type: "SAQUE",
      amount: -50.0,
      date: "03/06/2025 10:20",
      status: "PENDENTE",
      description: "Saque via PIX",
    },
  ]

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case "DEPOSITO":
      case "GANHO":
        return <ArrowDownLeft className="h-5 w-5 text-green-400" />
      case "APOSTA":
      case "SAQUE":
        return <ArrowUpRight className="h-5 w-5 text-red-400" />
      default:
        return <ArrowUpRight className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "CONCLUIDO":
        return "bg-green-500"
      case "PENDENTE":
        return "bg-yellow-500"
      case "CANCELADO":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const getAmountColor = (amount: number) => {
    return amount > 0 ? "text-green-400" : "text-red-400"
  }

  return (
    <div className="min-h-screen">
      <Header onMenuClick={() => setSidebarOpen(true)} onBetSlipClick={() => {}} />

      <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} user={user} />

      <main className="pt-20 pb-6 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold">Transações</h1>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filtrar
            </Button>
          </div>

          {/* Balance Summary */}
          <Card className="glass-effect border-white/20 p-6 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-sm text-gray-400">Saldo Atual</div>
                <div className="text-2xl font-bold text-green-400">R$ {user?.balance.toFixed(2)}</div>
              </div>
              <div className="text-center">
                <div className="text-sm text-gray-400">Total Depositado</div>
                <div className="text-2xl font-bold">R$ 500,00</div>
              </div>
              <div className="text-center">
                <div className="text-sm text-gray-400">Total Apostado</div>
                <div className="text-2xl font-bold">R$ 350,00</div>
              </div>
            </div>
          </Card>

          {/* Transactions List */}
          <div className="space-y-3">
            {transactions.map((transaction) => (
              <Card key={transaction.id} className="glass-effect border-white/20 p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="p-2 glass-effect rounded-full">{getTransactionIcon(transaction.type)}</div>
                    <div>
                      <div className="font-medium">{transaction.description}</div>
                      <div className="text-sm text-gray-400">
                        {transaction.date} • #{transaction.id}
                      </div>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className={`font-semibold ${getAmountColor(transaction.amount)}`}>
                      {transaction.amount > 0 ? "+" : ""}R$ {Math.abs(transaction.amount).toFixed(2)}
                    </div>
                    <Badge className={`${getStatusColor(transaction.status)} text-white text-xs`}>
                      {transaction.status}
                    </Badge>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
            <Button className="h-16 bg-green-600 hover:bg-green-700">
              <ArrowDownLeft className="h-6 w-6 mr-2" />
              Fazer Depósito
            </Button>
            <Button variant="outline" className="h-16">
              <ArrowUpRight className="h-6 w-6 mr-2" />
              Solicitar Saque
            </Button>
          </div>
        </div>
      </main>
    </div>
  )
}
