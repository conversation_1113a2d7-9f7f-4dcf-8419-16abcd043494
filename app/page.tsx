"use client"

import { useState } from "react"
import { Head<PERSON> } from "@/components/header"
import { Sidebar } from "@/components/sidebar"
import { MatchList } from "@/components/match-list"
import { BetSlip } from "@/components/bet-slip"
import { useAuth } from "@/hooks/use-auth"
import { LoginForm } from "@/components/login-form"

export default function HomePage() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [betSlipOpen, setBetSlipOpen] = useState(false)
  const { user, isAuthenticated } = useAuth()

  if (!isAuthenticated) {
    return <LoginForm />
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 to-gray-950">
      <Header onMenuClick={() => setSidebarOpen(true)} onBetSlipClick={() => setBetSlipOpen(true)} />

      <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} user={user} />

      <main className="pt-16 pb-20">
        <MatchList />
      </main>

      <BetSlip open={betSlipOpen} onClose={() => setBetSlipOpen(false)} />
    </div>
  )
}
