@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #16213e;
  --secondary: #1a1a2e;
  --accent: #4caf50;
  --warning: #ff9800;
  --danger: #f44336;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
  color: white;
  min-height: 100vh;
  font-family: "Inter", sans-serif;
  overflow-x: hidden;
}

.glass-effect {
  background: rgba(30, 41, 59, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gradient-text {
  background: linear-gradient(45deg, #4caf50, #8bc34a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Floating Install Button Styles */
.floating-install-btn {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 9998;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 16px 24px;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  animation: float 3s ease-in-out infinite;
}

.floating-install-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.6);
}

.floating-install-btn:active {
  transform: translateY(0) scale(0.98);
}

/* Floating animation */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
}

/* Pulse animation for attention */
@keyframes pulse {
  0%,
  100% {
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.8);
  }
}

.floating-install-btn.pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* Responsividade para dispositivos Samsung */

/* Galaxy S5 - 5.1" (360x640) */
@media screen and (max-width: 360px) and (max-height: 640px) {
  body {
    font-size: 13px;
  }

  .header {
    height: 56px;
    padding: 0 12px;
  }

  .match-card {
    padding: 12px;
    margin: 8px;
  }

  .bet-slip {
    width: 100%;
  }

  .floating-install-btn {
    bottom: 16px;
    right: 16px;
    padding: 12px 20px;
    font-size: 13px;
  }
}

/* Galaxy J7 - 5.5" (360x640) */
@media screen and (min-width: 360px) and (max-width: 375px) and (max-height: 667px) {
  .header {
    height: 60px;
    padding: 0 16px;
  }

  .match-card {
    padding: 14px;
    margin: 10px;
  }

  .floating-install-btn {
    bottom: 20px;
    right: 20px;
  }
}

/* Galaxy S8/S9 - 5.8" (360x740) */
@media screen and (min-width: 360px) and (max-width: 375px) and (min-height: 740px) and (max-height: 812px) {
  .header {
    padding-top: env(safe-area-inset-top, 20px);
  }

  .footer {
    padding-bottom: env(safe-area-inset-bottom, 20px);
  }

  .floating-install-btn {
    bottom: calc(24px + env(safe-area-inset-bottom, 0px));
    right: 24px;
  }
}

/* Galaxy S10 - 6.1" (360x760) */
@media screen and (min-width: 360px) and (max-width: 375px) and (min-height: 760px) and (max-height: 812px) {
  .header {
    padding-top: env(safe-area-inset-top, 24px);
  }

  .floating-install-btn {
    bottom: calc(24px + env(safe-area-inset-bottom, 0px));
  }
}

/* Galaxy S20/S21 - 6.2" (360-384x800-854) */
@media screen and (min-width: 360px) and (max-width: 384px) and (min-height: 800px) and (max-height: 854px) {
  .header {
    height: 64px;
    padding-top: env(safe-area-inset-top, 24px);
  }

  .match-card {
    padding: 16px;
    margin: 12px;
  }

  .bet-button {
    padding: 12px 16px;
    font-size: 14px;
  }

  .floating-install-btn {
    bottom: calc(28px + env(safe-area-inset-bottom, 0px));
    right: 28px;
  }
}

/* Galaxy Note 20 - 6.7" (412x915) */
@media screen and (min-width: 412px) and (min-height: 915px) {
  .header {
    height: 68px;
    padding: 0 20px;
    padding-top: env(safe-area-inset-top, 28px);
  }

  .match-card {
    padding: 18px;
    margin: 14px;
  }

  .bet-button {
    padding: 14px 18px;
    font-size: 15px;
  }

  .bet-slip {
    width: 380px;
  }

  .floating-install-btn {
    bottom: calc(32px + env(safe-area-inset-bottom, 0px));
    right: 32px;
    padding: 18px 28px;
    font-size: 15px;
  }
}

/* Orientação landscape para todos os dispositivos */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .header {
    height: 48px;
    padding-top: 8px;
  }

  .match-card {
    padding: 10px;
    margin: 6px;
  }

  .bet-slip {
    width: 320px;
  }

  .floating-install-btn {
    bottom: 16px;
    right: 16px;
    padding: 12px 20px;
    font-size: 13px;
  }
}

/* Melhorias gerais de responsividade */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }

  .container {
    padding: 0 16px;
  }

  .grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

@media (min-width: 1025px) {
  .grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
}

/* PWA Specific Styles */
@media (display-mode: standalone) {
  .header {
    padding-top: env(safe-area-inset-top);
  }

  .footer {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Remove scrollbar in standalone mode */
  ::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }

  /* Hide floating button in standalone mode */
  .floating-install-btn {
    display: none;
  }
}

/* Install prompt animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slideUp {
  animation: slideUp 0.5s ease-in-out;
}

.animate-zoomIn {
  animation: zoomIn 0.3s ease-in-out;
}

/* Bet slip responsive */
.bet-slip {
  position: fixed;
  right: 0;
  top: 0;
  height: 100vh;
  width: 350px;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  z-index: 999;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
}

.bet-slip.open {
  transform: translateX(0);
}

/* Match cards styling */
.match-card {
  background: #0f172a;
  border: 1px solid #1e293b;
  transition: all 0.3s ease;
  border-radius: 12px;
}

.match-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.league-header {
  background: linear-gradient(90deg, #f59e0b 0%, #fbbf24 100%);
  border-radius: 8px 8px 0 0;
}

.bet-button {
  transition: all 0.2s ease;
  border-radius: 8px;
  font-weight: 500;
}

.bet-button:hover {
  transform: scale(1.05);
}

.bet-button.selected {
  background: #4caf50 !important;
  border-color: #4caf50 !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

/* Touch improvements for mobile */
@media (hover: none) and (pointer: coarse) {
  .bet-button:hover {
    transform: none;
  }

  .bet-button:active {
    transform: scale(0.95);
  }

  .match-card:hover {
    transform: none;
  }

  .floating-install-btn:hover {
    transform: none;
  }

  .floating-install-btn:active {
    transform: scale(0.95);
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .logo,
  .team-logo {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Print styles */
@media print {
  body {
    background: white !important;
    color: black !important;
  }

  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  .floating-install-btn {
    display: none !important;
  }
}
