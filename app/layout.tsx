import type React from "react"
import type { <PERSON>ada<PERSON>, Viewport } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import "./sunmi.css"
import { PWAProvider } from "@/components/pwa-provider"
import ClientDownloadButton from "@/components/ClientDownloadButton"
import Script from "next/script"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Bolão Brasil - Sistema de Apostas Esportivas",
  description: "Aposte nos seus times favoritos com segurança e responsabilidade",
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "black-translucent",
    title: "Bolão Brasil",
    startupImage: [
      {
        url: "/icons/apple-splash-2048-2732.jpg",
        media:
          "(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
      },
      {
        url: "/icons/apple-splash-1668-2388.jpg",
        media:
          "(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
      },
      {
        url: "/icons/apple-splash-1536-2048.jpg",
        media:
          "(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
      },
      {
        url: "/icons/apple-splash-1125-2436.jpg",
        media:
          "(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)",
      },
      {
        url: "/icons/apple-splash-1242-2688.jpg",
        media:
          "(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)",
      },
      {
        url: "/icons/apple-splash-828-1792.jpg",
        media:
          "(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
      },
      {
        url: "/icons/apple-splash-1242-2208.jpg",
        media:
          "(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)",
      },
      {
        url: "/icons/apple-splash-750-1334.jpg",
        media:
          "(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
      },
      {
        url: "/icons/apple-splash-640-1136.jpg",
        media:
          "(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
      },
    ],
  },
  formatDetection: {
    telephone: true,
  },
  generator: "v0.dev",
  applicationName: "Bolão Brasil",
  referrer: "origin-when-cross-origin",
  keywords: ["apostas", "futebol", "esportes", "bolão", "brasil", "palpites"],
  authors: [{ name: "Bolão Brasil" }],
  creator: "Bolão Brasil",
  publisher: "Bolão Brasil",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
}

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#16213e" },
    { media: "(prefers-color-scheme: dark)", color: "#0f172a" },
  ],
  colorScheme: "dark",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="pt-BR">
      <head>
        {/* PWA Meta Tags */}
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-16x16.png" />
        <link rel="mask-icon" href="/icons/safari-pinned-tab.svg" color="#16213e" />
        <meta name="msapplication-TileColor" content="#16213e" />
        <meta name="msapplication-config" content="/browserconfig.xml" />

        {/* Android specific */}
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="application-name" content="Bolão Brasil" />

        {/* Samsung Internet */}
        <meta name="samsung-internet-app-capable" content="yes" />

        {/* Preload critical resources */}
        <link rel="preload" href="/icons/icon-192x192.png" as="image" />
        <link rel="preconnect" href="https://cdn.api-futebol.com.br" />

        {/* API Futebol Widget Script */}
        <Script src="https://cdn.api-futebol.com.br/widgets/v1/apifutebol-tabela.js" strategy="afterInteractive" />

        {/* PWA Install Helper for Android */}
        <Script src="/pwa-install-helper.js" strategy="afterInteractive" />
      </head>
      <body className={inter.className}>
        <PWAProvider>
          {children}
          <ClientDownloadButton />
        </PWAProvider>
      </body>
    </html>
  )
}
