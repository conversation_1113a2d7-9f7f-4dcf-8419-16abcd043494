# 📱 Guia para Gerar APK Android

## 🔧 Pré-requisitos

### 1. Instalar Android Studio
- Baixe em: https://developer.android.com/studio
- Instale com as configura<PERSON><PERSON><PERSON> padrão
- Aceite todas as licenças

### 2. Instalar Java JDK
- Baixe Java JDK 11 ou superior
- Configure a variável JAVA_HOME

### 3. Configurar Android SDK
- Abra Android Studio
- Vá em Tools > SDK Manager
- Instale Android SDK Platform 33 ou superior

## 🚀 Passos para Gerar APK

### 1. Configurar Capacitor
```bash
node scripts/setup-capacitor.js
```

### 2. Build da Aplicação
```bash
npm run build:mobile
```

### 3. Adicionar Plataforma Android
```bash
npx cap add android
```

### 4. Sincronizar Arquivos
```bash
npx cap sync android
```

### 5. Abrir no Android Studio
```bash
npx cap open android
```

### 6. Gerar APK no Android Studio
1. No Android Studio, aguarde a indexação
2. Vá em **Build > Build Bundle(s) / APK(s) > Build APK(s)**
3. Aguarde a compilação (pode demorar alguns minutos)
4. Clique em "locate" quando aparecer a notificação
5. O APK estará em: `android/app/build/outputs/apk/debug/app-debug.apk`

## 📋 Configurações Adicionais

### Personalizar Ícone do App
1. Substitua os arquivos em `android/app/src/main/res/mipmap-*/`
2. Use o Image Asset Studio do Android Studio

### Configurar Nome do App
Edite `android/app/src/main/res/values/strings.xml`:
```xml
<string name="app_name">Bolão Brasil</string>
```

### Configurar Splash Screen
Edite `android/app/src/main/res/values/styles.xml`

## 🔒 Build de Produção (Assinado)

### 1. Gerar Keystore
```bash
keytool -genkey -v -keystore bolao-brasil.keystore -alias bolao -keyalg RSA -keysize 2048 -validity 10000
```

### 2. Configurar build.gradle
Adicione as configurações de assinatura em `android/app/build.gradle`

### 3. Build Release
No Android Studio: **Build > Generate Signed Bundle / APK**

## 🛠️ Solução de Problemas

### Erro de Gradle
```bash
cd android
./gradlew clean
cd ..
npx cap sync android
```

### Erro de SDK
- Verifique se o Android SDK está instalado
- Configure as variáveis de ambiente ANDROID_HOME

### Erro de Licenças
```bash
cd android
./gradlew --stop
yes | sdkmanager --licenses
```

## 📱 Testar APK

### Instalar no Dispositivo
```bash
adb install android/app/build/outputs/apk/debug/app-debug.apk
```

### Ou copie o APK para o dispositivo e instale manualmente

## 🎯 Recursos do APK

- ✅ Funciona offline
- ✅ Ícone personalizado
- ✅ Splash screen
- ✅ Navegação nativa
- ✅ Acesso a recursos do dispositivo

---

**📱 APK do Bolão Brasil pronto para distribuição!**
