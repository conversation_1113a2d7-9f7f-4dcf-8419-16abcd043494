#!/bin/bash

# Script automático para build do APK otimizado para Sunmi V1s-G
# Android 6.0 Marshmallow, 32-bit ARM, baixa resolução

echo "🏗️ Build automático para Sunmi V1s-G iniciado..."

# Verificar se Android SDK está configurado
if [ -z "$ANDROID_HOME" ]; then
    echo "⚠️ Android SDK não configurado!"
    echo "🚀 Executando configuração automática..."
    chmod +x setup-android-auto.sh
    ./setup-android-auto.sh
    source ~/.bashrc
fi

# Verificar Java
if ! java -version 2>&1 | grep -q "1.8\|11"; then
    echo "⚠️ Java não encontrado ou versão incompatível!"
    echo "📥 Instalando OpenJDK 8..."
    sudo apt update && sudo apt install -y openjdk-8-jdk
    export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
fi

# 1. <PERSON><PERSON> builds anteriores
echo "🧹 <PERSON><PERSON><PERSON> builds anteriores..."
rm -rf out/
rm -rf android/app/build/
rm -rf .next/

# 2. Instalar/atualizar dependências
echo "📦 Verificando dependências..."
npm install --silent

# 3. Build do Next.js otimizado
echo "🔨 Fazendo build do Next.js..."
npm run build

# 4. Sincronizar com Capacitor
echo "🔄 Sincronizando com Capacitor..."
npx cap sync android

# 5. Configurar variáveis específicas para Sunmi
echo "⚙️ Configurando para Sunmi V1s-G..."

# Detectar ANDROID_HOME automaticamente
if [ -z "$ANDROID_HOME" ]; then
    if [ -d "/opt/android-sdk" ]; then
        export ANDROID_HOME="/opt/android-sdk"
    elif [ -d "$HOME/Android/Sdk" ]; then
        export ANDROID_HOME="$HOME/Android/Sdk"
    elif [ -d "/usr/lib/android-sdk" ]; then
        export ANDROID_HOME="/usr/lib/android-sdk"
    fi
fi

# Criar arquivo de configuração específico
cat > android/local.properties << EOF
# Configurações automáticas para Sunmi V1s-G
sdk.dir=$ANDROID_HOME
android.useAndroidX=true
android.enableJetifier=true

# Otimizações para dispositivos com pouca RAM
org.gradle.jvmargs=-Xmx1024m -XX:MaxPermSize=256m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.parallel=false
org.gradle.configureondemand=false
org.gradle.daemon=false

# Configurações específicas para Android 6.0
android.compileSdkVersion=28
android.targetSdkVersion=23
android.minSdkVersion=23
EOF

# 6. Build do APK
echo "🔨 Fazendo build do APK..."
cd android

# Dar permissão ao gradlew
chmod +x gradlew

# Limpar cache do Gradle
./gradlew clean

# Build debug para testes
echo "📱 Compilando APK para Sunmi V1s-G..."
./gradlew assembleDebug --stacktrace

cd ..

# 7. Verificar se o APK foi criado
APK_PATH="android/app/build/outputs/apk/debug/app-debug.apk"
if [ -f "$APK_PATH" ]; then
    echo ""
    echo "✅ APK criado com sucesso!"
    echo "📱 Localização: $APK_PATH"

    # Copiar APK para pasta pública
    mkdir -p public/downloads
    cp "$APK_PATH" public/downloads/bolao-brasil-sunmi.apk

    # Mostrar informações do APK
    echo "📊 Informações do APK:"
    ls -lh "$APK_PATH"

    # Verificar compatibilidade (se aapt estiver disponível)
    if command -v aapt &> /dev/null; then
        echo "🔍 Verificando compatibilidade..."
        aapt dump badging "$APK_PATH" | grep -E "(sdkVersion|targetSdkVersion|native-code)" || true
    fi

    echo ""
    echo "🎯 APK otimizado para Sunmi V1s-G:"
    echo "   ✅ Android 6.0 Marshmallow (API 23)"
    echo "   ✅ Arquitetura ARM 32-bit (armeabi-v7a)"
    echo "   ✅ Resolução baixa (480x800)"
    echo "   ✅ Otimizado para pouca RAM (1GB)"
    echo "   ✅ Disponível em: public/downloads/bolao-brasil-sunmi.apk"
    echo ""
    echo "📲 Para instalar no Sunmi V1s-G:"
    echo "   1. Via ADB: adb install $APK_PATH"
    echo "   2. Via Web: https://pos.mmapay.pro/downloads/bolao-brasil-sunmi.apk"
    echo "   3. Via QR Code: Será gerado automaticamente"

    # Gerar QR Code se qrencode estiver disponível
    if command -v qrencode &> /dev/null; then
        echo "📱 Gerando QR Code..."
        qrencode -t PNG -o public/downloads/qr-code.png "https://pos.mmapay.pro/downloads/bolao-brasil-sunmi.apk"
        echo "   QR Code: https://pos.mmapay.pro/downloads/qr-code.png"
    fi

else
    echo ""
    echo "❌ Erro ao criar APK!"
    echo "📋 Logs do erro:"
    if [ -f "android/app/build/outputs/logs/gradle-debug.log" ]; then
        tail -20 android/app/build/outputs/logs/gradle-debug.log
    fi
    echo ""
    echo "🔧 Possíveis soluções:"
    echo "   1. Verificar se Android SDK está instalado"
    echo "   2. Verificar se Java 8 está instalado"
    echo "   3. Executar: source ~/.bashrc"
    echo "   4. Tentar novamente: ./build-sunmi.sh"
fi

echo ""
echo "🏁 Build finalizado!"
