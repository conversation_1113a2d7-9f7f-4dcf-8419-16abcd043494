#!/bin/bash

# Script de correção rápida para erros específicos
echo "🚀 Correção rápida dos erros..."

# 1. Parar servidor se estiver rodando
pkill -f "next dev" 2>/dev/null || true

# 2. Limpar cache
echo "🧹 Limpando cache..."
rm -rf .next
rm -rf node_modules/.cache

# 3. Corrigir .npmrc
echo "⚙️ Corrigindo .npmrc..."
cat > .npmrc << 'EOF'
fund=false
audit-level=moderate
save-exact=true
prefer-offline=true
EOF

# 4. Verificar se ClientDownloadButton existe
if [ ! -f "components/ClientDownloadButton.tsx" ]; then
    echo "📱 Criando ClientDownloadButton..."
    cat > components/ClientDownloadButton.tsx << 'EOF'
'use client'

import dynamic from 'next/dynamic'

// Carregar DownloadAPKButton dinamicamente apenas no cliente
const DownloadAPKButton = dynamic(() => import('./DownloadAPKButton'), {
  ssr: false,
  loading: () => null
})

export default function ClientDownloadButton() {
  return <DownloadAPKButton />
}
EOF
fi

# 5. Verificar se ícones existem
echo "🎨 Verificando ícones..."
mkdir -p public/icons

if [ ! -f "public/icons/icon-192x192.png" ]; then
    echo "Criando ícone 192x192..."
    # Criar um ícone SVG simples
    cat > public/icons/icon-192x192.svg << 'EOF'
<svg width="192" height="192" viewBox="0 0 192 192" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="192" height="192" rx="24" fill="#1e40af"/>
<circle cx="96" cy="96" r="48" fill="white"/>
<text x="96" y="106" text-anchor="middle" fill="#1e40af" font-size="24" font-weight="bold">⚽</text>
</svg>
EOF
    # Copiar como PNG também
    cp public/icons/icon-192x192.svg public/icons/icon-192x192.png
fi

if [ ! -f "public/icons/icon-16x16.png" ]; then
    echo "Criando ícone 16x16..."
    cat > public/icons/icon-16x16.svg << 'EOF'
<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="16" height="16" rx="2" fill="#1e40af"/>
<circle cx="8" cy="8" r="4" fill="white"/>
</svg>
EOF
    cp public/icons/icon-16x16.svg public/icons/icon-16x16.png
fi

# 6. Verificar manifest.json
if [ ! -f "public/manifest.json" ]; then
    echo "📱 Criando manifest.json..."
    cat > public/manifest.json << 'EOF'
{
  "name": "Bolão Brasil",
  "short_name": "Bolão",
  "description": "Sistema de apostas esportivas",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#1e40af",
  "theme_color": "#1e40af",
  "icons": [
    {
      "src": "/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-16x16.png",
      "sizes": "16x16",
      "type": "image/png"
    }
  ]
}
EOF
fi

# 7. Instalar dependências se necessário
if [ ! -d "node_modules" ]; then
    echo "📦 Instalando dependências..."
    npm install --no-fund --no-audit
fi

# 8. Gerar Prisma Client
echo "🗄️ Gerando Prisma Client..."
npx prisma generate

# 9. Testar build
echo "🔨 Testando build..."
npm run build

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Correção concluída com sucesso!"
    echo ""
    echo "🚀 Para iniciar o servidor:"
    echo "   npm run dev"
    echo ""
    echo "🌐 Acesse: http://localhost:3000"
else
    echo ""
    echo "❌ Ainda há erros. Verifique os logs acima."
    echo ""
    echo "🔧 Tente executar manualmente:"
    echo "   npm install"
    echo "   npx prisma generate"
    echo "   npm run build"
fi

echo ""
echo "🏁 Script de correção rápida finalizado!"
